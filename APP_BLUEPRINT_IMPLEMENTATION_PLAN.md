# App Blueprint Generation Feature - Implementation Plan

## Overview

Implement an AI-powered App Blueprint generation feature for the onboarding step-four component with streaming output, editable interface, and clean UI design.

## Current State Analysis

### Backend Infrastructure

- **Existing Chat Module**: [`apps/core/src/chat/chat.module.ts`](apps/core/src/chat/chat.module.ts)
- **Streaming Service**: [`apps/core/src/chat/chat.service.ts`](apps/core/src/chat/chat.service.ts) with SSE implementation
- **AI Models**: [`apps/core/src/utils/models.ts`](apps/core/src/utils/models.ts) with OpenAI and Google models

### Frontend Infrastructure

- **Existing Streaming**: [`apps/admin/src/features/chat/main.tsx`](apps/admin/src/features/chat/main.tsx) using `@ai-sdk/react`
- **Lexical Editor**: [`apps/admin/src/features/onboarding/components/step-four.tsx`](apps/admin/src/features/onboarding/components/step-four.tsx)
- **Text Converter**: [`apps/admin/src/features/onboarding/hooks/lexical.ts`](apps/admin/src/features/onboarding/hooks/lexical.ts)

## Implementation Architecture

```mermaid
graph TD
    A[User reaches Step 4] --> B[Generate Blueprint Button]
    B --> C[Call Blueprint API with onboarding config]
    C --> D[Stream AI Response using @ai-sdk/react]
    D --> E[Display Blueprint Card with streaming content]
    E --> F{User satisfied?}
    F -->|Yes| G[Start Building Button]
    F -->|No| H[Edit Blueprint Button]
    H --> I[Slide-in Lexical Editor]
    I --> J[User edits content]
    J --> K[Submit edited content]
    K --> L[Update Blueprint Card]
    L --> F
```

## Backend Implementation

### 1. New Blueprint Generation Endpoint

**File**: [`apps/core/src/chat/chat.controller.ts`](apps/core/src/chat/chat.controller.ts)

```typescript
@Post('generate-blueprint')
@UseGuards(JwtAuthGuard)
async generateBlueprint(
  @Body() request: { onboardingConfig: OnboardingConfig },
  @Res() res: Response
) {
  try {
    this.setupSSEHeaders(res);
    res.status(HttpStatus.OK);
    return await this.chatService.streamBlueprintGeneration(
      request.onboardingConfig,
      res
    );
  } catch (error) {
    return this.handleChatError(error, res);
  }
}
```

### 2. Blueprint Streaming Service

**File**: [`apps/core/src/chat/chat.service.ts`](apps/core/src/chat/chat.service.ts)

```typescript
async streamBlueprintGeneration(
  config: OnboardingConfig,
  res: ExpressResponseWithFlush
) {
  try {
    const result = streamText({
      model: ai_models.openai['openai/gpt-4o-mini'],
      system: getBlueprintPrompt(),
      messages: [
        {
          role: 'user',
          content: `Generate an app blueprint for: ${JSON.stringify(config.data)}`
        }
      ],
    });

    // Handle streaming response (same pattern as existing chat)
    const aiResponse = result.toDataStreamResponse();
    // ... existing streaming logic
  } catch (error) {
    // ... error handling
  }
}
```

### 3. Blueprint Prompt Engineering

**File**: [`apps/core/src/utils/prompts.ts`](apps/core/src/utils/prompts.ts)

```typescript
export const getBlueprintPrompt = () => `
You are an expert app architect. Generate a comprehensive app blueprint based on the provided onboarding configuration.

Focus on:
1. App Name: Creative, relevant name based on the description
2. Core Features: 5-7 essential features with clear descriptions
3. Output Format: Structured JSON with appName and features array

Example Output:
{
  "appName": "TaskFlow Pro",
  "features": [
    {
      "name": "Task Management",
      "description": "Create, organize, and track tasks with priority levels"
    }
  ]
}
`;
```

## Frontend Implementation

### 4. Enhanced Step Four Component

**File**: [`apps/admin/src/features/onboarding/components/step-four.tsx`](apps/admin/src/features/onboarding/components/step-four.tsx)

#### Component State

```typescript
interface BlueprintData {
  appName: string;
  features: Array<{
    name: string;
    description: string;
  }>;
}

interface ComponentState {
  blueprint: BlueprintData | null;
  isGenerating: boolean;
  isEditing: boolean;
  streamedContent: string;
}
```

#### Streaming Integration

```typescript
const { messages, handleSubmit, status } = useChat({
  api: 'http://localhost:9700/api/chat/generate-blueprint',
  headers: {
    Authorization: `Bearer ${token}`,
    'Content-Type': 'application/json',
  },
  onFinish: (message) => {
    // Parse streamed blueprint data
    const blueprintData = parseBlueprintFromMessage(message.content);
    setBlueprint(blueprintData);
  },
});
```

### 5. Blueprint Card Component

**File**: [`apps/admin/src/features/onboarding/components/blueprint-card.tsx`](apps/admin/src/features/onboarding/components/blueprint-card.tsx)

```typescript
interface BlueprintCardProps {
  blueprint: BlueprintData;
  isStreaming: boolean;
  onEdit: () => void;
  onStartBuilding: () => void;
}

export const BlueprintCard = ({ blueprint, isStreaming, onEdit, onStartBuilding }: BlueprintCardProps) => {
  return (
    <div className="bg-black-100 border border-black-300 rounded-lg p-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold text-white">App Blueprint</h2>
        <button
          onClick={onEdit}
          className="flex items-center gap-2 text-gray-400 hover:text-white"
        >
          Customize <Edit className="w-4 h-4" />
        </button>
      </div>

      <h3 className="text-2xl font-bold text-white mb-6">
        {isStreaming ? <StreamingText content={blueprint.appName} /> : blueprint.appName}
      </h3>

      <div className="space-y-4">
        <h4 className="text-lg font-medium text-gray-300">FEATURES</h4>
        {blueprint.features.map((feature, index) => (
          <FeatureItem
            key={index}
            feature={feature}
            isStreaming={isStreaming && index === blueprint.features.length - 1}
          />
        ))}
      </div>

      <button
        onClick={onStartBuilding}
        className="w-full mt-6 bg-white text-black py-3 rounded-lg font-medium hover:bg-gray-100"
      >
        Start Building
      </button>
    </div>
  );
};
```

### 6. Slide-in Editor Panel

**File**: [`apps/admin/src/features/onboarding/components/editor-panel.tsx`](apps/admin/src/features/onboarding/components/editor-panel.tsx)

```typescript
interface EditorPanelProps {
  isOpen: boolean;
  initialContent: string;
  onSave: (content: string) => void;
  onCancel: () => void;
}

export const EditorPanel = ({ isOpen, initialContent, onSave, onCancel }: EditorPanelProps) => {
  return (
    <div className={cn(
      "fixed left-0 top-0 h-full w-1/2 bg-black-200 border-r border-black-300 transform transition-transform duration-300 z-50",
      isOpen ? "translate-x-0" : "-translate-x-full"
    )}>
      <div className="p-6 h-full flex flex-col">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-white">Edit Blueprint</h3>
          <button onClick={onCancel} className="text-gray-400 hover:text-white">
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="flex-1">
          <Editor
            editorSerializedState={initialContent}
            onSerializedChange={(value) => {
              // Handle editor changes
            }}
          />
        </div>

        <div className="flex gap-3 mt-4">
          <button
            onClick={onCancel}
            className="flex-1 py-2 border border-black-300 rounded-lg text-white hover:bg-black-300"
          >
            Cancel
          </button>
          <button
            onClick={() => onSave(extractedContent)}
            className="flex-1 py-2 bg-white text-black rounded-lg hover:bg-gray-100"
          >
            Save
          </button>
        </div>
      </div>
    </div>
  );
};
```

### 7. Updated Step Four Layout

```typescript
export const OnboardingStepFour = ({ onSubmit, isPending }: Props) => {
  const [blueprint, setBlueprint] = useState<BlueprintData | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [hasGenerated, setHasGenerated] = useState(false);

  const handleGenerateBlueprint = () => {
    setHasGenerated(true);
    // Trigger streaming API call
    handleSubmit(undefined, {
      body: { onboardingConfig: sampleOnboardingConfig }
    });
  };

  const handleEditBlueprint = () => {
    setIsEditing(true);
  };

  const handleSaveEdit = (editedContent: string) => {
    // Parse edited content and update blueprint
    const updatedBlueprint = parseEditedContent(editedContent);
    setBlueprint(updatedBlueprint);
    setIsEditing(false);
  };

  return (
    <div className="relative">
      {/* Editor Panel - slides in from left */}
      <EditorPanel
        isOpen={isEditing}
        initialContent={blueprintToLexicalContent(blueprint)}
        onSave={handleSaveEdit}
        onCancel={() => setIsEditing(false)}
      />

      {/* Main Content */}
      <div className={cn(
        "transition-all duration-300",
        isEditing ? "ml-1/2" : "ml-0"
      )}>
        {!hasGenerated ? (
          <div className="text-center">
            <h2 className="text-2xl font-bold text-white mb-4">
              Generate Your App Blueprint
            </h2>
            <p className="text-gray-400 mb-8">
              Based on your onboarding configuration, we'll create a comprehensive app blueprint
            </p>
            <button
              onClick={handleGenerateBlueprint}
              className="bg-white text-black px-8 py-3 rounded-lg font-medium hover:bg-gray-100"
            >
              Generate Blueprint
            </button>
          </div>
        ) : (
          <BlueprintCard
            blueprint={blueprint}
            isStreaming={status === 'streaming'}
            onEdit={handleEditBlueprint}
            onStartBuilding={() => onSubmit({})}
          />
        )}
      </div>
    </div>
  );
};
```

## Styling & UX

### Design System

- **Colors**: Black background (`bg-black-100`), white text, gray accents
- **Typography**: Clean, modern fonts with proper hierarchy
- **Animations**: Smooth slide-in transitions, streaming text effects
- **Layout**: Responsive design with proper spacing

### Streaming Effects

- **Typewriter Effect**: Characters appear progressively during streaming
- **Loading States**: Subtle animations for generating content
- **Real-time Updates**: Immediate UI updates as content streams

### Editor Integration

- **Slide Animation**: Smooth 300ms transition from left
- **Split Layout**: Editor on left, blueprint card on right when editing
- **Content Sync**: Real-time synchronization between editor and card

## Data Flow

### Generation Flow

```
sampleOnboardingConfig → API Call → AI Streaming → Blueprint Card → UI Update
```

### Edit Flow

```
Blueprint Data → Lexical Editor → User Edits → Content Parsing → Blueprint Update → Card Refresh
```

## Error Handling

### Network Errors

- Retry mechanism with exponential backoff
- User-friendly error messages
- Graceful degradation to default blueprint

### Streaming Interruption

- Partial content preservation
- Resume capability where possible
- Clear user feedback on connection issues

### Editor Errors

- Input validation and sanitization
- Error boundaries for component isolation
- Fallback to previous valid state

## Performance Considerations

### Streaming Optimization

- Efficient chunk processing
- Memory management for long streams
- Connection pooling and reuse

### UI Performance

- Virtual scrolling for large feature lists
- Debounced editor updates
- Hardware-accelerated animations

### Bundle Size

- Lazy loading of editor components
- Code splitting for streaming utilities
- Tree shaking of unused AI SDK features

## Testing Strategy

### Unit Tests

- Blueprint parsing functions
- Streaming response handlers
- Editor content conversion utilities

### Integration Tests

- API endpoint streaming behavior
- Editor-to-blueprint synchronization
- Error handling scenarios

### E2E Tests

- Complete user flow from generation to editing
- Cross-browser streaming compatibility
- Performance under various network conditions

## Implementation Timeline

### Phase 1: Backend API (2-3 days)

1. Add blueprint generation endpoint
2. Implement streaming service method
3. Create blueprint-specific prompts
4. Test streaming functionality

### Phase 2: Frontend Core (3-4 days)

1. Update step-four component structure
2. Implement blueprint card component
3. Add streaming integration
4. Basic UI styling

### Phase 3: Editor Integration (2-3 days)

1. Create slide-in editor panel
2. Implement content synchronization
3. Add edit/save functionality
4. Polish animations and transitions

### Phase 4: Testing & Polish (1-2 days)

1. Comprehensive testing
2. Performance optimization
3. Error handling refinement
4. Final UI polish

## Success Metrics

### Functional Requirements

- ✅ AI generates relevant app blueprints
- ✅ Streaming works smoothly without interruption
- ✅ Editor allows seamless content modification
- ✅ UI matches design specifications

### Performance Requirements

- ✅ Streaming latency < 200ms first chunk
- ✅ Editor opens/closes < 300ms
- ✅ No memory leaks during long sessions
- ✅ Responsive on mobile devices

### User Experience

- ✅ Intuitive workflow from generation to editing
- ✅ Clear visual feedback during all states
- ✅ Graceful error handling and recovery
- ✅ Consistent with existing app design patterns
