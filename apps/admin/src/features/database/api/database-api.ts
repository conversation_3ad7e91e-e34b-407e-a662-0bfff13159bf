import { api } from '@/lib/api-client';
import { useBaseStore } from '@/store/base-store';
import { useQuery } from '@tanstack/react-query';

export type TableInfo = {
  table_name: string;
  table_type: string;
};

export type ColumnInfo = {
  column_name: string;
  data_type: string;
  is_nullable: string;
  column_default: string | null;
  ordinal_position: number;
};

// API functions
const getTables = async (workspaceId: string): Promise<TableInfo[]> => {
  return api.get(`/supabase/tables/${workspaceId}`);
};

const getTableStructure = async (
  workspaceId: string,
  tableName: string,
): Promise<ColumnInfo[]> => {
  return api.get(`/supabase/table-structure/${workspaceId}/${tableName}`);
};

const getTableData = async (
  workspaceId: string,
  tableName: string,
  limit: number = 100,
): Promise<any[]> => {
  return api.get(`/supabase/table-data/${workspaceId}/${tableName}`, {
    params: { limit },
  });
};

// Hooks
export const useTables = () => {
  const { workspaceId } = useBaseStore();

  return useQuery({
    queryKey: ['database-tables', workspaceId],
    queryFn: () => getTables(workspaceId as string),
    enabled: !!workspaceId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

export const useTableStructure = (tableName: string | null) => {
  const { workspaceId } = useBaseStore();

  return useQuery({
    queryKey: ['table-structure', workspaceId, tableName],
    queryFn: () =>
      getTableStructure(workspaceId as string, tableName as string),
    enabled: !!workspaceId && !!tableName,
    staleTime: 10 * 60 * 1000, // 10 minutes - structure doesn't change often
    retry: 2,
  });
};

export const useTableData = (tableName: string | null, limit: number = 100) => {
  const { workspaceId } = useBaseStore();

  return useQuery({
    queryKey: ['table-data', workspaceId, tableName, limit],
    queryFn: () =>
      getTableData(workspaceId as string, tableName as string, limit),
    enabled: !!workspaceId && !!tableName,
    staleTime: 2 * 60 * 1000, // 2 minutes - data changes more frequently
    retry: 2,
  });
};
