export interface TableInfo {
  table_name: string;
  table_type: string;
}

export interface ColumnInfo {
  column_name: string;
  data_type: string;
  is_nullable: string;
  column_default: string | null;
  ordinal_position: number;
}

export interface DatabaseError {
  message: string;
  code?: string;
  details?: string;
}

export interface TableDataRow {
  [key: string]: any;
}

export interface DatabasePageState {
  selectedTable: string | null;
  isLoading: boolean;
  error: DatabaseError | null;
}
