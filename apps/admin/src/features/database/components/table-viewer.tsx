import { DataTable } from '@/components/shared/data-table';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle, Database } from 'lucide-react';
import { useMemo } from 'react';
import { useTableData, useTableStructure } from '../api/database-api';

interface TableViewerProps {
  selectedTable: string | null;
}

export function TableViewer({ selectedTable }: TableViewerProps) {
  const {
    data: structure,
    isLoading: structureLoading,
    error: structureError,
  } = useTableStructure(selectedTable);
  const {
    data: tableData,
    isLoading: dataLoading,
    error: dataError,
  } = useTableData(selectedTable);

  const isLoading = structureLoading || dataLoading;
  const error = structureError || dataError;

  const columns = useMemo(() => {
    if (!structure) return [];

    return structure.map((column) => ({
      accessorKey: column.column_name,
      header: () => (
        <div className="text-left">
          <div className="font-medium text-gray-200">{column.column_name}</div>
          <div className="text-xs text-gray-500 font-normal">
            {column.data_type}
            {column.is_nullable === 'NO' && (
              <span className="text-orange-400 ml-1">*</span>
            )}
          </div>
        </div>
      ),
      cell: ({ row }: any) => {
        const value = row.getValue(column.column_name);
        return (
          <div className="text-sm">
            {value === null ? (
              <span className="text-gray-500 italic">null</span>
            ) : value === undefined ? (
              <span className="text-gray-500 italic">undefined</span>
            ) : typeof value === 'object' ? (
              <span className="text-blue-400 font-mono text-xs">
                {JSON.stringify(value)}
              </span>
            ) : typeof value === 'boolean' ? (
              <span className={value ? 'text-green-400' : 'text-red-400'}>
                {String(value)}
              </span>
            ) : typeof value === 'number' ? (
              <span className="text-yellow-400">{String(value)}</span>
            ) : (
              <span className="text-gray-300">{String(value)}</span>
            )}
          </div>
        );
      },
    }));
  }, [structure]);

  if (!selectedTable) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center text-gray-400">
          <Database className="w-16 h-16 mx-auto mb-4 opacity-50" />
          <p className="text-lg">Select a table to view its data</p>
          <p className="text-sm text-gray-500 mt-2">
            Choose a table from the sidebar to explore its structure and data
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center text-red-400">
          <AlertCircle className="w-16 h-16 mx-auto mb-4" />
          <p className="text-lg">Failed to load table data</p>
          <p className="text-sm text-gray-500 mt-2">
            {error?.message || 'Unknown error occurred'}
          </p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="mb-6">
          <Skeleton className="h-8 w-48 mb-2 bg-gray-800" />
          <Skeleton className="h-4 w-32 bg-gray-800" />
        </div>
        <div className="space-y-2">
          {Array.from({ length: 10 }).map((_, i) => (
            <Skeleton key={i} className="h-12 w-full bg-gray-800" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-white mb-2">{selectedTable}</h1>
        <div className="flex items-center gap-4 text-sm text-gray-400">
          <span>{tableData?.length || 0} rows</span>
          <span>•</span>
          <span>{structure?.length || 0} columns</span>
          {tableData && tableData.length >= 100 && (
            <>
              <span>•</span>
              <span className="text-orange-400">Showing first 100 rows</span>
            </>
          )}
        </div>
      </div>

      <div className="bg-[#1a1d23] rounded-lg border border-gray-800 overflow-hidden">
        <DataTable
          columns={columns}
          data={tableData || []}
          className="[&_th]:bg-[#0f1114] [&_th]:text-gray-200 [&_th]:font-medium [&_tr]:border-gray-700 [&_tr:hover]:bg-gray-800/50"
        />
      </div>
    </div>
  );
}
