import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle, Database } from 'lucide-react';
import { TableInfo } from '../api/database-api';

interface DatabaseSidebarProps {
  tables: TableInfo[];
  selectedTable: string | null;
  onTableSelect: (tableName: string) => void;
  isLoading: boolean;
  error: any;
}

export function DatabaseSidebar({
  tables,
  selectedTable,
  onTableSelect,
  isLoading,
  error,
}: DatabaseSidebarProps) {
  return (
    <div className="w-64 bg-[#090D12] border-r border-gray-800 flex flex-col">
      <div className="p-4 border-b border-gray-800">
        <div className="flex items-center gap-2">
          <Database className="w-5 h-5 text-orange-500" />
          <h2 className="text-lg font-semibold text-white">Database Tables</h2>
        </div>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-2">
          {isLoading ? (
            <div className="space-y-2">
              {Array.from({ length: 5 }).map((_, i) => (
                <Skeleton key={i} className="h-8 w-full bg-gray-800" />
              ))}
            </div>
          ) : error ? (
            <div className="p-4 text-center">
              <AlertCircle className="w-8 h-8 text-red-500 mx-auto mb-2" />
              <p className="text-sm text-red-400 mb-2">Failed to load tables</p>
              <p className="text-xs text-gray-500">
                {error?.message || 'Unknown error occurred'}
              </p>
            </div>
          ) : tables.length === 0 ? (
            <div className="p-4 text-center">
              <Database className="w-8 h-8 text-gray-500 mx-auto mb-2" />
              <p className="text-sm text-gray-400">No tables found</p>
              <p className="text-xs text-gray-500">
                Create some tables in your Supabase project
              </p>
            </div>
          ) : (
            <div className="space-y-1">
              {tables.map((table) => (
                <button
                  key={table.table_name}
                  onClick={() => onTableSelect(table.table_name)}
                  className={`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                    selectedTable === table.table_name
                      ? 'bg-orange-500/20 text-orange-400 border border-orange-500/30'
                      : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <Database className="w-4 h-4" />
                    <span className="truncate">{table.table_name}</span>
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>
      </ScrollArea>

      {tables.length > 0 && (
        <div className="p-4 border-t border-gray-800">
          <p className="text-xs text-gray-500">
            {tables.length} table{tables.length !== 1 ? 's' : ''} found
          </p>
        </div>
      )}
    </div>
  );
}
