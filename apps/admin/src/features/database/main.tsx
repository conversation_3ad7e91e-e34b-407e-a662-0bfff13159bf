import DashboardHeader from '@/components/shared/DashboardHeader';
import { Button } from '@/components/ui/button';
import { useSupabaseConnection } from '@/features/workspace/api/supabase-connect';
import { AlertCircle, Database } from 'lucide-react';
import { useState } from 'react';
import { useNavigate } from 'react-router';
import { useTables } from './api/database-api';
import { DatabaseSidebar } from './components/database-sidebar';
import { TableViewer } from './components/table-viewer';

export default function DatabasePage() {
  const [selectedTable, setSelectedTable] = useState<string | null>(null);
  const navigate = useNavigate();
  const { data: connection, isLoading: connectionLoading } =
    useSupabaseConnection();
  const { data: tables, isLoading, error } = useTables();

  // Check if Supabase is connected
  if (connectionLoading) {
    return (
      <div className="flex flex-col h-screen bg-[#0f1114] text-white">
        <DashboardHeader />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <Database className="w-16 h-16 mx-auto mb-4 opacity-50" />
            <p className="text-lg text-gray-400">
              Checking database connection...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (!connection?.connected) {
    return (
      <div className="flex flex-col h-screen bg-[#0f1114] text-white">
        <DashboardHeader />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-md">
            <AlertCircle className="w-16 h-16 mx-auto mb-4 text-orange-500" />
            <h2 className="text-xl font-semibold mb-2">
              Database Not Connected
            </h2>
            <p className="text-gray-400 mb-6">
              You need to connect your Supabase database before you can view
              tables and data.
            </p>
            <Button
              onClick={() => navigate('/integrations?highlighted=supabase')}
              className="bg-orange-600 hover:bg-orange-700"
            >
              Connect Supabase
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-[#0f1114] text-white">
      <DashboardHeader />
      <div className="flex-1 flex">
        <DatabaseSidebar
          tables={tables || []}
          selectedTable={selectedTable}
          onTableSelect={setSelectedTable}
          isLoading={isLoading}
          error={error}
        />
        <div className="flex-1">
          <TableViewer selectedTable={selectedTable} />
        </div>
      </div>
    </div>
  );
}
