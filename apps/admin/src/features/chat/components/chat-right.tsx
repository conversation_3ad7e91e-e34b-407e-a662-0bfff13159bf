import { Label } from '@/components/ui/label';
import { SquareSwitch } from '@/components/ui/square-switch';
import { Tree, TreeDataItem } from '@/components/ui/tree';
import { cn } from '@/lib/utils';
import { Editor, useMonaco } from '@monaco-editor/react';
import { shikiToMonaco } from '@shikijs/monaco';
import {
  ChevronRight,
  Copy,
  Download,
  FileText,
  GitBranch,
} from 'lucide-react';
import { Fragment, useEffect, useId, useState } from 'react';
import { createHighlighter } from 'shiki';
import { beautifyCode } from '../utils/cleanCode';
import { getFileContentByPath } from '../utils/convert';
import { getLanguage } from '../utils/getLanguage';
import { BuildProgress, BuildProgressMessage } from './build-progress';

export const ChatRight = ({
  treeData,
  gitFiles,
  setSelectedFile,
  setLanguage,
  previewRef,
  language,
  selectedFile,
  view,
  setView,
  lastMessage,
  previewUrl,
  error,
  setError,
  logs,
  setLogs,
  showLogs,
  setShowLogs,
}: {
  treeData: TreeDataItem[] | TreeDataItem;
  gitFiles: Record<string, string>;
  setSelectedFile: (file: { path: string; content: string }) => void;
  setLanguage: (language: string) => void;
  previewRef: React.RefObject<HTMLIFrameElement>;
  language: string;
  selectedFile: { path: string; content: string };
  view: 'code' | 'preview';
  setView: (view: 'code' | 'preview') => void;
  lastMessage: any; // WebSocket message
  previewUrl: string;
  error: string | undefined;
  setError: React.Dispatch<React.SetStateAction<string | undefined>>;
  logs: string | undefined;
  setLogs: React.Dispatch<React.SetStateAction<string | undefined>>;
  showLogs: boolean;
  setShowLogs: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const id = useId();
  const monaco = useMonaco();
  const [themeLoaded, setThemeLoaded] = useState(false);
  const [buildCompleted, setBuildCompleted] = useState(false);
  const [currentBuildMessage, setCurrentBuildMessage] =
    useState<BuildProgressMessage | null>(null);

  useEffect(() => {
    const setupShiki = async () => {
      if (!monaco) return;

      const highlighter = await createHighlighter({
        themes: ['one-dark-pro'],
        langs: ['typescript', 'javascript', 'html', 'css', 'json', 'markdown'],
      });
      shikiToMonaco(highlighter, monaco);
      setThemeLoaded(true);
    };

    setupShiki();
  }, [monaco]);

  // Handle WebSocket messages for build progress display only
  useEffect(() => {
    if (lastMessage) {
      try {
        const parsed = JSON.parse(lastMessage.data);

        if (parsed.type === 'build-status') {
          setCurrentBuildMessage(parsed);

          if (parsed.data.status === 'completed') {
            setBuildCompleted(true);
          } else {
            setBuildCompleted(false);
          }
        }
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    }
  }, [lastMessage]);

  return (
    <div className="w-[55%] h-[88vh] border border-black-300 rounded-lg mr-4">
      {/* Header */}
      <div className="flex justify-between items-center p-4 bg-black-100">
        <div className="inline-flex items-center gap-2">
          <SquareSwitch
            id={id}
            checked={view === 'preview'}
            onCheckedChange={() =>
              setView(view === 'preview' ? 'code' : 'preview')
            }
            aria-label="Toggle switch"
          />
          <Label className="text-white font-light" htmlFor={id}>
            <span className="sr-only">Toggle switch</span>
            {view === 'preview' ? <span>Preview</span> : <span>Code</span>}
          </Label>
        </div>

        {/* icons */}
        <div className="flex gap-4">
          <GitBranch className="w-5 h-5" />
          <Download className="w-5 h-5" />
        </div>
      </div>

      {/* Code View */}
      <div className={cn('flex h-[80vh]', view === 'code' ? 'flex' : 'hidden')}>
        <Tree
          data={treeData}
          className="flex-shrink-0 w-[250px] h-full border"
          initialSlelectedItemId="f12"
          onSelectChange={(item) => {
            if (!item?.children) {
              // MIGRATION: Use new getFileContentByPath with gitFiles
              const rawContent =
                getFileContentByPath(gitFiles, item?.path ?? '') ?? '';
              setSelectedFile({
                path: item?.path ?? '',
                content: beautifyCode(rawContent, item?.path ?? ''),
              });
              const fileExtension = item?.path;
              setLanguage(getLanguage(fileExtension));
            }
          }}
          folderIcon={FileText}
          itemIcon={FileText}
          expandAll={false}
        />

        <div className="flex-1 border-y border-r h-full">
          <div className="flex flex-col h-full">
            <div className="flex justify-between text-sm items-center px-4 py-4">
              <FilePath path={selectedFile.path} />

              <div className="flex gap-2 text-white-100">
                <Copy className="w-4 h-4" />
                <Download className="w-4 h-4" />
              </div>
            </div>

            {themeLoaded && (
              <Editor
                height="100%"
                language={language}
                value={selectedFile.content}
                options={{
                  readOnly: true,
                  minimap: { enabled: false },
                }}
                theme={'one-dark-pro'}
              />
            )}
          </div>
        </div>
      </div>

      {/* preview View */}
      <div
        className={cn('flex h-[80vh]', view === 'preview' ? 'flex' : 'hidden')}
      >
        {currentBuildMessage && !buildCompleted ? (
          <BuildProgress
            messages={[currentBuildMessage]}
            error={error}
            setError={setError}
            logs={logs}
            setLogs={setLogs}
            showLogs={showLogs}
            setShowLogs={setShowLogs}
          />
        ) : (
          <iframe
            ref={previewRef}
            src={previewUrl || 'about:blank'}
            className="w-full h-full bg-white border"
            title="Preview"
          />
        )}
      </div>
    </div>
  );
};

const FilePath = ({ path }: { path: string }) => {
  const parts = path.split('/');

  return (
    <div className="flex items-center flex-wrap text-sm text-white-100">
      {parts.map((part, index) => (
        <Fragment key={index}>
          <span>{part}</span>
          {index < parts.length - 1 && (
            <ChevronRight className="mx-1 w-3 h-3" />
          )}
        </Fragment>
      ))}
    </div>
  );
};
