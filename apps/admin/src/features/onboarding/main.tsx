import {
  useGenerateModuleDetails,
  useGenerateProjectBlueprint,
} from '@/api/project-planning';
import { Button } from '@/components/ui/button';
import { toast } from '@/lib/toast';
import { cn } from '@/lib/utils';
import { useBaseStore } from '@/store/base-store';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router';
import { useGetMe } from '../workspace/api/get-me';
import { useUpdateOnboarding } from './api/mutations';
import BackIcon from './assets/back.svg';
import { OnboardingStepFive } from './components/step-five';
import { OnboardingStepFour } from './components/step-four';
import { OnboardingStepOne } from './components/step-one';
import { OnboardingStepThree } from './components/step-three';
import { OnboardingStepTwo } from './components/step-two';
import { useGetOnboardingProgress } from './hooks/get-onboarding-progress';

const Steps = ({
  active,
  onClick,
}: {
  active: boolean;
  onClick: () => void;
}) => {
  return (
    <Button
      variant={'outline'}
      className={cn(
        'w-20 border h-1 py-0 rounded-sm',
        active ? 'bg-primary hover:bg-primary/90' : 'hover:bg-inherit',
      )}
      onClick={onClick}
    ></Button>
  );
};

export const On1boarding = () => {
  const me = useGetMe();
  const { workspaceId: selectedWorkspaceId } = useBaseStore((state) => state);
  const workspaceLength = Number(me.data?.workspaces?.length);
  const hasSingleWorkspace = workspaceLength === 1;
  const workspaceId = hasSingleWorkspace
    ? me.data?.workspaces?.[0]?.id
    : selectedWorkspaceId;
  const updateOnboarding = useUpdateOnboarding();
  const getOnboardingProgress = useGetOnboardingProgress();
  const generateBlueprint = useGenerateProjectBlueprint();
  const generateModuleDetails = useGenerateModuleDetails();
  const [currentStep, setCurrentStep] = useState(1);
  const navigate = useNavigate();
  const FINAL_INPUT_STEP = 4;

  useEffect(() => {
    const onboardingData = getOnboardingProgress.data?.data;

    if (onboardingData?.name || onboardingData?.appDescription) {
      setCurrentStep(2);
    }

    if (onboardingData?.brandColor || onboardingData?.brandVoice) {
      setCurrentStep(3);
    }

    if (onboardingData?.competitors || onboardingData?.targetCustomers) {
      setCurrentStep(4);
    }
  }, [getOnboardingProgress.data?.data]);

  const onSubmit = async (values: Record<string, string>) => {
    if (!workspaceId) {
      toast.error('Workspace ID is required');
      return;
    }

    updateOnboarding.mutate(
      {
        id: workspaceId,
        data:
          currentStep === FINAL_INPUT_STEP
            ? { ...values, isCompleted: true }
            : values,
      },
      {
        onSuccess: async () => {
          if (currentStep === FINAL_INPUT_STEP) {
            setCurrentStep((state) => state + 1);

            try {
              // Phase 1: Generate blueprint with modules
              const blueprintMessage = `This is the onboarding data given to us by the user. Create a detailed project plan based on the following requirements, ${JSON.stringify({ ...getOnboardingProgress.data?.data, ...values })}`;

              const blueprint = await generateBlueprint.mutateAsync({
                message: blueprintMessage,
                workspaceId,
              });

              // Phase 2: Initiate detail generation for the first module
              if (blueprint.modules && blueprint.modules.length > 0) {
                const firstModule = blueprint.modules[0];
                if (firstModule && firstModule.id) {
                  // Store the first module's ID in base-store
                  useBaseStore.getState().setModuleId(firstModule.id);

                  try {
                    // Generate first module stories
                    await generateModuleDetails.mutateAsync({
                      moduleId: firstModule.id,
                      workspaceId,
                    });
                  } catch (moduleError) {
                    console.error(
                      `Failed to initiate detail generation for first module ${firstModule.id}:`,
                      moduleError,
                    );
                    // Log error, but do not block navigation or overall success message.
                  }
                } else {
                  console.warn(
                    'Blueprint generated, but the first module or its ID is missing.',
                  );
                }
              } else {
                console.warn(
                  'Blueprint generated, but no modules found to pre-generate details for.',
                );
              }

              navigate('/');
              toast.success('Project plan generated successfully!');
            } catch (error: any) {
              // This catch primarily handles errors from generateBlueprint
              console.error('Project planning error:', error);
              getOnboardingProgress.refetch(); // Refetch onboarding data if blueprint fails
              setCurrentStep(FINAL_INPUT_STEP); // Optionally revert to the review step
              toast.error(error?.message || 'Failed to generate project plan');
            }
            return;
          }

          // continue to next step
          setCurrentStep((state) => state + 1);
          getOnboardingProgress.refetch();
        },
        onError: (error) => {
          // This onError is for updateOnboarding.mutate
          toast.error(String(error)); // Ensure error is string
        },
      },
    );
  };

  const handleRetryProjectPlanning = async () => {
    if (!workspaceId) {
      toast.error('Workspace ID is required');
      return;
    }

    try {
      // Phase 1: Generate blueprint with modules
      const blueprintMessage = `This is the onboarding data given to us by the user. Create a detailed project plan based on the following requirements, ${JSON.stringify({ ...getOnboardingProgress.data?.data })}`;

      const blueprint = await generateBlueprint.mutateAsync({
        message: blueprintMessage,
        workspaceId,
      });

      // Phase 2: Initiate detail generation for the first module only (fire-and-forget)
      if (blueprint.modules && blueprint.modules.length > 0) {
        const firstModule = blueprint.modules[0];
        if (firstModule && firstModule.id) {
          // Store the first module's ID in base-store
          useBaseStore.getState().setModuleId(firstModule.id);
          try {
            // Fire-and-forget: no await here
            generateModuleDetails.mutateAsync({
              moduleId: firstModule.id,
              workspaceId,
            });
          } catch (moduleError) {
            console.error(
              `Failed to initiate detail generation for first module ${firstModule.id} on retry:`,
              moduleError,
            );
          }
        } else {
          console.warn(
            'Blueprint (retry) generated, but the first module or its ID is missing.',
          );
        }
      } else {
        console.warn(
          'Blueprint (retry) generated, but no modules found to pre-generate details for.',
        );
      }

      navigate('/');
      toast.success('Project plan generated successfully!');
    } catch (error: any) {
      // This catch primarily handles errors from generateBlueprint on retry
      console.error('Project planning retry error:', error);
      toast.error(error?.message || 'Failed to generate project plan on retry');
    }
  };

  return (
    <div className="mx-auto bg-[#0D1117] h-screen">
      {/* header  */}
      <div className="py-12 relative right-6">
        <button></button>
        {/* Heading */}
        <div className="flex gap-4 items-center justify-center">
          {currentStep !== 5 && currentStep !== 1 && (
            <Button
              onClick={() => {
                if (currentStep > 1) {
                  setCurrentStep((state) => state - 1);
                }
              }}
              variant={'link'}
            >
              <img src={BackIcon} alt="" className="scale-75" />
            </Button>
          )}

          <h3 className="font-outfit font-light">
            {currentStep === 1 && 'Get you Know Better'}
            {currentStep === 2 && 'Your Brand Personality'}
            {currentStep === 3 && 'Your Audience & Goal'}
            {currentStep === 4 && 'Review Your App Plan'}
            {currentStep === 5 && 'Setting Up Your App'}
          </h3>
        </div>

        {/* Steps indicators */}
        <div className="flex gap-3 items-center justify-center py-4 relative left-3">
          <Steps active={currentStep >= 1} onClick={() => {}} />
          <Steps active={currentStep >= 2} onClick={() => {}} />
          <Steps active={currentStep >= 3} onClick={() => {}} />
          <Steps active={currentStep >= 4} onClick={() => {}} />
          <Steps active={currentStep >= 5} onClick={() => {}} />
        </div>
      </div>

      {/* form body */}
      <div
        className={cn(
          'max-w-[350px] mx-auto flex-col items-center mt-16',
          currentStep === 4 && 'max-w-[1350px] mt-2',
          currentStep === 5 && 'max-w-[550px] flex mt-0',
        )}
      >
        {currentStep === 1 && (
          <OnboardingStepOne
            onSubmit={onSubmit}
            isPending={updateOnboarding.isPending}
          />
        )}

        {currentStep === 2 && (
          <OnboardingStepTwo
            onSubmit={onSubmit}
            isPending={updateOnboarding.isPending}
          />
        )}

        {currentStep === 3 && (
          <OnboardingStepThree
            onSubmit={onSubmit}
            isPending={updateOnboarding.isPending}
          />
        )}

        {currentStep === 4 && (
          <OnboardingStepFour
            onSubmit={onSubmit}
            isPending={updateOnboarding.isPending}
          />
        )}

        {currentStep === 5 && (
          <OnboardingStepFive
            handleRetryProjectPlanning={handleRetryProjectPlanning}
            // Updated props for OnboardingStepFive
            isError={generateBlueprint.isError} // Primarily reflects blueprint generation error
            isPending={generateBlueprint.isPending} // Primarily reflects blueprint generation pending
            onboardingData={getOnboardingProgress.data}
          />
        )}
      </div>
    </div>
  );
};
