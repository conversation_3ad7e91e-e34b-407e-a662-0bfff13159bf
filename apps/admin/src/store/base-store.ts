import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface BaseState {
  workspaceId: string | null;
  setWorkspaceId: (id: string | null) => void;
  supabaseCodeVerifier: string | null;
  setSupabaseCodeVerifier: (verifier: string | null) => void;
  moduleId: string | null; // New: Represents the currently selected/active module
  setModuleId: (id: string | null) => void; // New: Action to set it
  showSupabaseCard: boolean; // New: Show Supabase connection reminder card in chat
  setShowSupabaseCard: (show: boolean) => void; // New: Action to set it
  showErrorCard: boolean; // New: Show error resolution card in chat
  setShowErrorCard: (show: boolean) => void; // New: Action to set it
  sandboxId: string | null; // Daytona sandbox ID
  setSandboxId: (id: string | null) => void;
  previewUrl: string | null; // Daytona preview URL
  setPreviewUrl: (url: string | null) => void;
  clearAll: () => void; // New: Action to clear all state on logout
}

export const useBaseStore = create<BaseState>()(
  persist(
    (set) => ({
      workspaceId: null,
      // Modified: Reset moduleId when workspaceId changes and handle git workspace switching
      setWorkspaceId: (id) => {
        set({ workspaceId: id, moduleId: null });
      },
      supabaseCodeVerifier: null,
      setSupabaseCodeVerifier: (verifier) =>
        set({ supabaseCodeVerifier: verifier }),
      moduleId: null, // Initialize new state
      setModuleId: (id) => set({ moduleId: id }), // Implement new action
      showSupabaseCard: false, // Initialize new state - default to false (not shown)
      setShowSupabaseCard: (show) => set({ showSupabaseCard: show }), // Implement new action
      showErrorCard: false, // Initialize new state - default to false (not shown)
      setShowErrorCard: (show) => set({ showErrorCard: show }), // Implement new action
      sandboxId: null, // Initialize Daytona sandbox ID
      setSandboxId: (id) => set({ sandboxId: id }),
      previewUrl: null, // Initialize Daytona preview URL
      setPreviewUrl: (url) => set({ previewUrl: url }),
      // Clear all state to default values (used on logout)
      clearAll: () =>
        set({
          workspaceId: null,
          supabaseCodeVerifier: null,
          moduleId: null,
          showSupabaseCard: false,
          showErrorCard: false,
          sandboxId: null,
          previewUrl: null,
        }),
    }),
    {
      name: 'base-storage', // name of the item in the storage (must be unique)
    },
  ),
);
