import { Button } from '@/components/ui/button';
import Integrations from '@/features/home/<USER>/integrations.svg';
import Project from '@/features/home/<USER>/project.svg';
import { Database } from 'lucide-react';
import { useLocation, useNavigate } from 'react-router';

export default function DashboardHeader() {
  const navigate = useNavigate();
  const location = useLocation();

  // Determine active tab based on current route
  const getActiveTab = () => {
    if (location.pathname === '/integrations') return 'integrations';
    if (location.pathname === '/database') return 'database';
    return 'project';
  };

  const activeTab = getActiveTab();

  const handleTabClick = (tab: 'project' | 'integrations' | 'database') => {
    if (tab === 'project') {
      navigate('/');
    } else if (tab === 'integrations') {
      navigate('/integrations');
    } else if (tab === 'database') {
      navigate('/database');
    }
  };

  return (
    <div className="w-full border-b border-gray-800 py-4 bg-[#0f1114]">
      <div className="flex justify-center items-center gap-8 relative">
        <div
          className="flex flex-col items-center gap-2 cursor-pointer"
          onClick={() => handleTabClick('project')}
        >
          <img
            src={Project}
            alt=""
            className={activeTab === 'project' ? 'opacity-100' : 'opacity-50'}
          />
          <span
            className={`text-sm font-medium ${activeTab === 'project' ? 'text-white' : 'text-gray-500'}`}
          >
            Project Overviews
          </span>
        </div>

        <div
          className="flex flex-col items-center gap-2 cursor-pointer"
          onClick={() => handleTabClick('integrations')}
        >
          <img
            src={Integrations}
            alt=""
            className={
              activeTab === 'integrations' ? 'opacity-100' : 'opacity-50'
            }
          />
          <span
            className={`text-sm font-medium ${activeTab === 'integrations' ? 'text-white' : 'text-gray-500'}`}
          >
            Integration
          </span>
        </div>

        <div
          className="flex flex-col items-center gap-2 cursor-pointer"
          onClick={() => handleTabClick('database')}
        >
          <Database
            className={`w-6 h-6 ${activeTab === 'database' ? 'opacity-100 text-white' : 'opacity-50 text-gray-500'}`}
          />
          <span
            className={`text-sm font-medium ${activeTab === 'database' ? 'text-white' : 'text-gray-500'}`}
          >
            Database
          </span>
        </div>

        <div className="absolute right-6">
          <Button className="bg-orange-600 hover:bg-orange-700 text-white">
            Deploy
          </Button>
        </div>
      </div>
    </div>
  );
}
