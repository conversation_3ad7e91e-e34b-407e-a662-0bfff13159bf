import AuthLayout from '@/components/layouts/auth/index.tsx';
import DashboardLayout from '@/components/layouts/dashboard/index.tsx';
import { On1boarding } from '@/features/onboarding/main';
import { lazy } from 'react';
import { Navigate, RouteObject } from 'react-router';

// Lazy load components for better performance
const Home = lazy(() => import('@/features/home/<USER>'));
const Profile = lazy(() => import('@/features/profile/main'));
const Workspace = lazy(() => import('@/features/workspace/main'));
const Projects = lazy(() => import('@/features/projects/main'));
const Chat = lazy(() => import('@/features/chat/main'));
const Integrations = lazy(
  () => import('@/features/workspace/pages/integration-page'),
);
const Database = lazy(() => import('@/features/database/main'));
const Login = lazy(() => import('@/features/auth/pages/login'));
const Signup = lazy(() => import('@/features/auth/pages/signup'));
const Verify = lazy(() => import('@/features/auth/pages/verify'));
const TwoFactorVerify = lazy(
  () => import('@/features/auth/pages/two-factor-verify'),
);
const TwoFactorRecovery = lazy(
  () => import('@/features/auth/pages/two-factor-recovery'),
);

export const routes: RouteObject[] = [
  {
    path: '/',
    element: <DashboardLayout />,
    children: [
      { index: true, element: <Home /> },
      { path: 'integrations', element: <Integrations /> },
      { path: 'database', element: <Database /> },
      { path: 'profile', element: <Profile /> },
      { path: 'workspace', element: <Workspace /> },
      { path: 'chat', element: <Chat /> },
      { path: 'onboarding', element: <On1boarding /> },
      { path: 'projects', element: <Projects /> },
    ],
  },
  {
    path: 'auth',
    element: <AuthLayout />,
    children: [
      { path: 'login', element: <Login /> },
      { path: 'signup', element: <Signup /> },
      { path: 'two-factor-verify', element: <TwoFactorVerify /> },
      { path: 'two-factor-recovery', element: <TwoFactorRecovery /> },
      { path: 'verify', element: <Verify /> },
      { path: '*', element: <Navigate to="/auth/login" replace /> }, // Catch-all for auth
    ],
  },
  { path: '*', element: <Navigate to="/" replace /> }, // Global fallback
];
