{"name": "<PERSON><PERSON><PERSON>", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/jest/bin/jest.js --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migration:dev": "cd ../../packages/database && npx prisma migrate dev --name init", "migration:deploy": "cd ../../packages/database && npx prisma migrate deploy --name init"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/google": "^1.2.18", "@ai-sdk/openai": "^1.3.20", "@daytonaio/sdk": "^0.24.2", "@iammoderator/db": "workspace:*", "@nestjs-modules/ioredis": "^2.0.2", "@nestjs/bull": "^11.0.2", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^3.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/typeorm": "^11.0.0", "@octokit/rest": "^18.12.0", "@openrouter/ai-sdk-provider": "^0.7.2", "@requesty/ai-sdk": "^0.0.7", "@supabase/supabase-js": "^2.49.4", "@swc/core": "^1.3.100", "@typescript-eslint/parser": "^8.34.1", "ai": "^4.3.5", "axios": "^1.7.9", "bcrypt": "^5.1.1", "bull": "^4.16.5", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "esbuild": "^0.19.12", "eslint": "^8.57.1", "eslint-config-next": "^15.3.4", "eslint-plugin-react": "^7.33.0", "ioredis": "^5.4.2", "multer": "1.4.5-lts.1", "oauth2_client": "^0.0.3", "otpauth": "^9.3.5", "passport-local": "^1.0.0", "pg": "^8.16.0", "reflect-metadata": "^0.2.0", "repomix": "^0.3.7", "rxjs": "^7.8.1", "simple-oauth2": "^5.1.0", "supabase-management-js": "^1.0.0", "tailwindcss": "^3.4.16", "typeorm": "^0.3.23", "uuid": "^11.1.0", "zod": "^3.24.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@types/uuid": "^10.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "packageManager": "pnpm@9.0.0"}