import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import { Client } from 'pg';
import { PrismaService } from 'src/persistence/prisma/prisma.service';
import { SupabaseManagementAPI } from 'supabase-management-js';

@Injectable()
export class SupabaseService {
  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
  ) {}

  async getAuthorizationUrl() {
    const clientId = this.configService.get<string>('SUPA_CONNECT_CLIENT_ID');
    const redirectUri = this.configService.get<string>(
      'SUPA_CONNECT_REDIRECT_URI',
    );

    // Generate a code verifier (PKCE)
    const codeVerifier = this.generateCodeVerifier();
    const codeChallenge = await this.generateCodeChallenge(codeVerifier);

    const authUrl = new URL('https://api.supabase.com/v1/oauth/authorize');
    authUrl.searchParams.append('client_id', clientId);
    authUrl.searchParams.append('redirect_uri', redirectUri);
    authUrl.searchParams.append('response_type', 'code');
    authUrl.searchParams.append('code_challenge', codeChallenge);
    authUrl.searchParams.append('code_challenge_method', 'S256');

    return { uri: authUrl.toString(), codeVerifier };
  }

  async exchangeCodeForToken(code: string, codeVerifier: string) {
    const clientId = this.configService.get<string>('SUPA_CONNECT_CLIENT_ID');
    const clientSecret = this.configService.get<string>(
      'SUPA_CONNECT_CLIENT_SECRET',
    );
    const redirectUri = this.configService.get<string>(
      'SUPA_CONNECT_REDIRECT_URI',
    );

    const response = await fetch('https://api.supabase.com/v1/oauth/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Accept: 'application/json',
        Authorization: `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code,
        redirect_uri: redirectUri,
        code_verifier: codeVerifier,
      }),
    });

    return response.json();
  }

  async saveSupabaseConnection(workspaceId: string, tokenData: any) {
    const expiresAt = new Date();
    expiresAt.setSeconds(expiresAt.getSeconds() + tokenData.expires_in);

    // Get Supabase project info
    console.log('calling supabase management api');
    const supaManagementClient = new SupabaseManagementAPI({
      accessToken: tokenData.access_token,
    });

    await this.prisma.workspaceSupabaseConnection.upsert({
      where: { workspaceId },
      create: {
        workspaceId,
        accessToken: tokenData.access_token,
        refreshToken: tokenData.refresh_token,
        expiresAt,
      },
      update: {
        accessToken: tokenData.access_token,
        refreshToken: tokenData.refresh_token,
        expiresAt,
      },
    });

    const workspace = await this.prisma.workspace.findUnique({
      where: { id: workspaceId },
    });
    const organizations = await supaManagementClient.getOrganizations();
    const organization = organizations[0];
    //create a project with this workspace
    const project = await supaManagementClient.createProject({
      organization_id: organization.id,
      name: workspace.name,
      region: 'ap-south-1',
      db_pass: 'password@123',
      plan: 'free',
    });
    await this.prisma.workspaceSupabaseConnection.update({
      where: { workspaceId },
      data: {
        organizationId: organization.id,
        project,
      },
    });

    return workspace;
  }

  async refreshToken(workspaceId: string) {
    const connection = await this.prisma.workspaceSupabaseConnection.findUnique(
      {
        where: { workspaceId },
      },
    );

    if (!connection) {
      throw new Error('No Supabase connection found for this workspace');
    }

    const clientId = this.configService.get<string>('SUPA_CONNECT_CLIENT_ID');
    const clientSecret = this.configService.get<string>(
      'SUPA_CONNECT_CLIENT_SECRET',
    );

    const response = await fetch('https://api.supabase.com/v1/oauth/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Accept: 'application/json',
        Authorization: `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
      },
      body: new URLSearchParams({
        grant_type: 'refresh_token',
        refresh_token: connection.refreshToken,
      }),
    });

    const tokenData = await response.json();
    const expiresAt = new Date();
    expiresAt.setSeconds(expiresAt.getSeconds() + tokenData.expires_in);

    return this.prisma.workspaceSupabaseConnection.update({
      where: { workspaceId },
      data: {
        accessToken: tokenData.access_token,
        refreshToken: tokenData.refresh_token,
        expiresAt,
      },
    });
  }

  // Helper methods for PKCE
  private generateCodeVerifier() {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Buffer.from(array).toString('base64url');
  }

  private async generateCodeChallenge(verifier: string) {
    const encoder = new TextEncoder();
    const data = encoder.encode(verifier);
    const digest = await crypto.subtle.digest('SHA-256', data);
    return Buffer.from(digest).toString('base64url');
  }

  async getConnection(workspaceId: string) {
    const connection = await this.prisma.workspaceSupabaseConnection.findUnique(
      {
        where: { workspaceId },
      },
    );

    if (!connection) {
      return { connected: false };
    }

    return { connected: true, connection };
  }

  async migrate(workspaceId: string, sql: string) {
    const connection = await this.prisma.workspaceSupabaseConnection.findUnique(
      {
        where: { workspaceId },
      },
    );

    if (!connection) {
      throw new Error('No Supabase connection found for this workspace');
    }

    const project = connection.project as any;

    const projectId = project.id;
    const dbUser = 'postgres';
    const dbPassword = 'password@123'; // you should store/retrieve this securely
    const dbName = 'postgres';

    const client = new Client({
      host: 'aws-0-ap-south-1.pooler.supabase.com',
      port: 6543,
      user: `postgres.${projectId}`,
      password: dbPassword,
      database: 'postgres',
      ssl: { rejectUnauthorized: false },
    });

    try {
      await client.connect();
      await client.query(sql);
      console.log('Migration executed successfully');
    } catch (err) {
      console.error('Migration failed:', err);
      throw err;
    } finally {
      await client.end();
    }
  }

  private async createDatabaseClient(workspaceId: string): Promise<Client> {
    const connection = await this.prisma.workspaceSupabaseConnection.findUnique(
      {
        where: { workspaceId },
      },
    );

    if (!connection) {
      throw new Error('No Supabase connection found for this workspace');
    }

    const project = connection.project as any;
    const projectId = project.id;
    const dbPassword = 'password@123';

    return new Client({
      host: 'aws-0-ap-south-1.pooler.supabase.com',
      port: 6543,
      user: `postgres.${projectId}`,
      password: dbPassword,
      database: 'postgres',
      ssl: { rejectUnauthorized: false },
    });
  }

  async getPublicTables(workspaceId: string) {
    const client = await this.createDatabaseClient(workspaceId);

    try {
      await client.connect();
      const result = await client.query(`
        SELECT table_name, table_type
        FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_type = 'BASE TABLE'
        ORDER BY table_name
      `);
      return result.rows;
    } catch (err) {
      console.error('Failed to fetch public tables:', err);
      throw err;
    } finally {
      await client.end();
    }
  }

  async getTableStructure(workspaceId: string, tableName: string) {
    const client = await this.createDatabaseClient(workspaceId);

    try {
      await client.connect();
      const result = await client.query(
        `
        SELECT
          column_name,
          data_type,
          is_nullable,
          column_default,
          ordinal_position
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = $1
        ORDER BY ordinal_position
      `,
        [tableName],
      );
      return result.rows;
    } catch (err) {
      console.error(`Failed to fetch table structure for ${tableName}:`, err);
      throw err;
    } finally {
      await client.end();
    }
  }

  async getTableData(
    workspaceId: string,
    tableName: string,
    limit: number = 100,
  ) {
    const client = await this.createDatabaseClient(workspaceId);

    try {
      await client.connect();
      // Use parameterized query to prevent SQL injection
      const result = await client.query(
        `SELECT * FROM public."${tableName}" LIMIT $1`,
        [limit],
      );
      return result.rows;
    } catch (err) {
      console.error(`Failed to fetch data from table ${tableName}:`, err);
      throw err;
    } finally {
      await client.end();
    }
  }
}
