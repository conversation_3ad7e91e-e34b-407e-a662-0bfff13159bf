import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from '@shared-library/guard/jwt.guard';
import { SupabaseService } from './supabase.service';

@Controller('supabase')
@UseGuards(JwtAuthGuard)
export class SupabaseController {
  constructor(private readonly supabaseService: SupabaseService) {}

  @Get('authorize')
  async getAuthorizationUrl() {
    return this.supabaseService.getAuthorizationUrl();
  }

  @Post('callback')
  async exchangeCodeForToken(
    @Body() body: { code: string; codeVerifier: string; workspaceId: string },
  ) {
    const tokenData = await this.supabaseService.exchangeCodeForToken(
      body.code,
      body.codeVerifier,
    );
    console.log('tokenData', tokenData);
    return this.supabaseService.saveSupabaseConnection(
      body.workspaceId,
      tokenData,
    );
  }

  @Get('connection')
  async getConnection(@Query('workspaceId') workspaceId: string) {
    return this.supabaseService.getConnection(workspaceId);
  }

  @Post('migrate')
  async migrate(@Body() body: { workspaceId: string; sql: string }) {
    return this.supabaseService.migrate(body.workspaceId, body.sql);
  }

  @Get('tables/:workspaceId')
  async getTables(@Param('workspaceId') workspaceId: string) {
    return this.supabaseService.getPublicTables(workspaceId);
  }

  @Get('table-structure/:workspaceId/:tableName')
  async getTableStructure(
    @Param('workspaceId') workspaceId: string,
    @Param('tableName') tableName: string,
  ) {
    return this.supabaseService.getTableStructure(workspaceId, tableName);
  }

  @Get('table-data/:workspaceId/:tableName')
  async getTableData(
    @Param('workspaceId') workspaceId: string,
    @Param('tableName') tableName: string,
    @Query('limit') limit?: string,
  ) {
    const limitNum = limit ? parseInt(limit, 10) : 100;
    return this.supabaseService.getTableData(workspaceId, tableName, limitNum);
  }
}
