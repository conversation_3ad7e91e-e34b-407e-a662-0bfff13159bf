import { createOpenRouter } from '@openrouter/ai-sdk-provider';
import { requesty } from '@requesty/ai-sdk';
import { LanguageModelV1 } from 'ai';

const openrouter = createOpenRouter({
  apiKey: process.env.OPENROUTER_API_KEY,
});

type AiModels = {
  openai: {
    'openai/gpt-4o-mini': LanguageModelV1;
    'openai/o4-mini': LanguageModelV1;
  };
  google: {
    'google/gemini-2.5-flash': LanguageModelV1;
    'google/gemini-2.5-pro-preview': LanguageModelV1;
  };
  anthropic: {
    'anthropic/claude-sonnet-4': LanguageModelV1;
  };
  deepseek: {
    'deepseek-r1-0528': LanguageModelV1;
  };
};

export const ai_models: AiModels = {
  openai: {
    'openai/gpt-4o-mini': openrouter('openai/gpt-4o-mini'),
    'openai/o4-mini': openrouter('openai/o4-mini'),
  },
  google: {
    'google/gemini-2.5-flash': openrouter('google/gemini-2.5-flash'),
    'google/gemini-2.5-pro-preview': openrouter(
      'google/gemini-2.5-pro-preview',
    ),
  },
  deepseek: {
    'deepseek-r1-0528': openrouter('deepseek/deepseek-r1-0528'),
  },
  anthropic: {
    'anthropic/claude-sonnet-4': requesty('anthropic/claude-sonnet-4'),
  },
};
