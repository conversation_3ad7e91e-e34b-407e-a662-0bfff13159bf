import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Logger,
  Param,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from '../../libs/shared/src/guard/jwt.guard';
import { ApiRequest } from '../../libs/shared/src/modules/auth/interfaces/jwt-payload.interface';
import { PreviewService } from './preview.service';
import { DaytonaService } from './daytona.service';

@Controller('preview')
export class PreviewController {
  private readonly logger = new Logger(PreviewController.name);

  constructor(
    private readonly previewService: PreviewService,
    private readonly daytonaService: DaytonaService,
  ) {}

  @Get('health')
  async healthCheck(@Query('previewUrl') previewUrl: string) {
    return this.daytonaService.healthCheckWithTimeout(previewUrl);
  }

  /**
   * Create Daytona sandbox
   */
  @Post('create-sandbox')
  @UseGuards(JwtAuthGuard)
  async createSandbox(@Req() req: ApiRequest) {
    const userId = req.user.userId;
    this.logger.log(`Creating Daytona sandbox for user ${userId}`);

    try {
      const result = await this.daytonaService.createSandbox();
      this.logger.log(`Sandbox created successfully: ${result.sandboxId}`);
      return result;
    } catch (error) {
      this.logger.error('Sandbox creation failed:', error);
      throw new HttpException(
        `Sandbox creation failed: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Upload files to Daytona sandbox
   */
  @Post('upload-files')
  @UseGuards(JwtAuthGuard)
  async uploadFiles(
    @Body()
    { files, sandboxId }: { files: Record<string, string>; sandboxId: string },
    @Req() req: ApiRequest,
  ) {
    const userId = req.user.userId;
    this.logger.log(
      `Uploading files to sandbox ${sandboxId} for user ${userId}`,
    );

    try {
      if (!files || typeof files !== 'object') {
        throw new HttpException(
          'Invalid files provided',
          HttpStatus.BAD_REQUEST,
        );
      }

      if (!sandboxId) {
        throw new HttpException(
          'Sandbox ID is required',
          HttpStatus.BAD_REQUEST,
        );
      }

      const result = await this.daytonaService.uploadFiles(sandboxId, files);
      this.logger.log(
        `Files uploaded successfully: ${result.filesCount} files`,
      );
      return result;
    } catch (error) {
      this.logger.error('File upload failed:', error);
      throw new HttpException(
        `File upload failed: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Destroy Daytona sandbox
   */
  @Delete('destroy-sandbox/:sandboxId')
  @UseGuards(JwtAuthGuard)
  async destroySandbox(@Param('sandboxId') sandboxId: string) {
    this.logger.log(`Destroying sandbox: ${sandboxId}`);

    try {
      await this.daytonaService.destroySandbox(sandboxId);
      return {
        message: `Sandbox ${sandboxId} destroyed successfully`,
        sandboxId,
      };
    } catch (error) {
      this.logger.error('Sandbox destruction failed:', error);
      throw new HttpException(
        `Sandbox destruction failed: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get logs from Daytona sandbox
   */
  @Get('logs/:sandboxId')
  @UseGuards(JwtAuthGuard)
  async getLogs(@Param('sandboxId') sandboxId: string, @Req() req: ApiRequest) {
    const userId = req.user.userId;
    this.logger.log(`Getting logs for sandbox ${sandboxId} for user ${userId}`);

    try {
      if (!sandboxId) {
        throw new HttpException(
          'Sandbox ID is required',
          HttpStatus.BAD_REQUEST,
        );
      }

      const result = await this.daytonaService.getLogs(sandboxId);
      this.logger.log(`Logs retrieved successfully for sandbox ${sandboxId}`);
      return result;
    } catch (error) {
      this.logger.error('Failed to get logs:', error);
      throw new HttpException(
        `Failed to get logs: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
