// =================== PREVIEW SERVICE RESPONSE TYPES ===================

export interface PreviewResult {
  machineId: string;
  appName: string;
  previewUrl: string;
  wsUrl: string;
  filesCount: number;
  provider: 'fly';
  runtimeErrors: RuntimeError[];
  hasRuntimeErrors: boolean;
}

export interface RuntimeError {
  message: string;
  stack?: string;
  timestamp: string;
  type: 'error' | 'warning';
}

// =================== HEALTH CHECK RESPONSE TYPES ===================

export interface HealthCheckResult {
  status: 'ok' | 'no_machine' | 'healthy' | 'unhealthy';
  message: string;
  appName: string;
  appUrl?: string;
  machineRestarted?: boolean;
  error?: string;
  timestamp: string;
}

export interface ServiceHealthCheck {
  status: 'ok';
  service: 'fly-preview';
  timestamp: string;
}

// =================== MACHINE MANAGEMENT TYPES ===================

export interface MachineResult {
  machineId: string;
  appName: string;
  appUrl: string;
  wsUrl: string;
  message: string;
}

export interface UserMachineRecord {
  id: string;
  userId: number;
  appName: string;
  machineId: string;
  status: MachineStatus;
  createdAt: Date;
  updatedAt: Date;
}

export type MachineStatus = 'creating' | 'running' | 'suspended' | 'destroyed';

// =================== USER TYPES ===================

export interface UserWithFlyApp {
  fly_app_name: string;
}

// =================== CLEANUP TYPES ===================

export interface CleanupResult {
  success: boolean;
  message: string;
  userId: number;
  machineId?: string;
  appName?: string;
}

// ----------------------- FUNCTION PARAMETER INTERFACES -----------------------

export interface CreatePreviewForUserParams {
  files: Record<string, string>;
  userId: number;
}

export interface SuspendMachineParams {
  appName: string;
  machineId: string;
}

export interface SetUserMachineParams {
  userId: number;
  appName: string;
  machineId: string;
}

export interface UpdateMachineStatusParams {
  userId: number;
  status: MachineStatus;
}

export interface CreateNewMachineForUserParams {
  userId: number;
  appName: string;
  appUrl: string;
}

export interface HandleExistingMachineParams {
  userId: number;
  currentMachine: UserMachineRecord;
  appName: string;
  appUrl: string;
}

export interface ReactivateMachineParams {
  userId: number;
  currentMachine: UserMachineRecord;
  appName: string;
  appUrl: string;
}

export interface ReplaceBrokenMachineParams {
  userId: number;
  currentMachine: UserMachineRecord;
  appName: string;
  appUrl: string;
}

export interface BuildMachineResponseParams {
  machineId: string;
  appName: string;
  appUrl: string;
  message: string;
}

// =================== MACHINE LOGS TYPES ===================

export interface LogEntry {
  id: string;
  instanceId: string;
  level: string;
  message: string;
  region: string;
  timestamp: string;
}

export interface MachineLogsResult {
  logs: LogEntry[];
  success: boolean;
  error?: string;
}
