import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { PreviewService } from './preview.service';
import { DaytonaService } from './daytona.service';
import { PrismaService } from '../persistence/prisma/prisma.service';
import type {
  DaytonaSandboxResult,
  DaytonaUploadResult,
  DaytonaHealthCheckResult,
} from './daytona.service';

describe('PreviewService', () => {
  let service: PreviewService;
  let mockDaytonaService: jest.Mocked<DaytonaService>;
  let mockPrisma: jest.Mocked<PrismaService>;

  beforeEach(async () => {
    const mockDaytonaServiceInstance = {
      createSandbox: jest.fn(),
      uploadFiles: jest.fn(),
      destroySandbox: jest.fn(),
      healthCheck: jest.fn(),
      healthCheckWithTimeout: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PreviewService,
        {
          provide: DaytonaService,
          useValue: mockDaytonaServiceInstance,
        },
        {
          provide: PrismaService,
          useValue: {
            // Add any Prisma methods that might be used
            $connect: jest.fn(),
            $disconnect: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<PreviewService>(PreviewService);
    mockDaytonaService = module.get(
      DaytonaService,
    ) as jest.Mocked<DaytonaService>;
    mockPrisma = module.get(PrismaService) as jest.Mocked<PrismaService>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createPreview', () => {
    it('should create preview successfully', async () => {
      const mockFiles = {
        'src/App.tsx':
          'export default function App() { return <div>Hello</div>; }',
      };

      const mockSandboxResult: DaytonaSandboxResult = {
        sandboxId: 'test-sandbox-123',
        previewUrl: 'https://test-sandbox.daytona.dev',
        previewToken: 'test-token-123',
        provider: 'daytona',
        message: 'Daytona sandbox created successfully',
      };

      const mockUploadResult: DaytonaUploadResult = {
        success: true,
        filesCount: 1,
        message: 'Files uploaded successfully',
      };

      mockDaytonaService.createSandbox.mockResolvedValue(mockSandboxResult);
      mockDaytonaService.uploadFiles.mockResolvedValue(mockUploadResult);
      mockDaytonaService.healthCheckWithTimeout.mockResolvedValue();

      const result = await service.createPreview(mockFiles);

      expect(result).toEqual({
        sandboxId: 'test-sandbox-123',
        previewUrl: 'https://test-sandbox.daytona.dev',
        previewToken: 'test-token-123',
        provider: 'daytona',
        message: 'Daytona sandbox created successfully',
        filesCount: 1,
        success: true,
      });

      expect(mockDaytonaService.createSandbox).toHaveBeenCalledTimes(1);
      expect(mockDaytonaService.uploadFiles).toHaveBeenCalledWith(
        'test-sandbox-123',
        mockFiles,
      );
      expect(mockDaytonaService.healthCheckWithTimeout).toHaveBeenCalledWith(
        'https://test-sandbox.daytona.dev',
      );
    });

    it('should handle preview creation failure', async () => {
      const mockFiles = {
        'src/App.tsx':
          'export default function App() { return <div>Hello</div>; }',
      };

      mockDaytonaService.createSandbox.mockRejectedValue(
        new Error('Sandbox creation failed'),
      );

      await expect(service.createPreview(mockFiles)).rejects.toThrow(
        'Preview creation failed: Sandbox creation failed',
      );

      expect(mockDaytonaService.createSandbox).toHaveBeenCalledTimes(1);
      expect(mockDaytonaService.uploadFiles).not.toHaveBeenCalled();
      expect(mockDaytonaService.healthCheckWithTimeout).not.toHaveBeenCalled();
    });
  });

  describe('uploadFilesToSandbox', () => {
    it('should upload files to sandbox successfully', async () => {
      const sandboxId = 'test-sandbox-123';
      const mockFiles = { 'src/utils.ts': 'export const util = () => "test";' };

      const mockUploadResult: DaytonaUploadResult = {
        success: true,
        filesCount: 1,
        message: 'Files uploaded successfully',
      };

      mockDaytonaService.uploadFiles.mockResolvedValue(mockUploadResult);

      const result = await service.uploadFilesToSandbox(sandboxId, mockFiles);

      expect(result).toEqual(mockUploadResult);
      expect(mockDaytonaService.uploadFiles).toHaveBeenCalledWith(
        sandboxId,
        mockFiles,
      );
    });
  });

  describe('destroySandbox', () => {
    it('should destroy sandbox successfully', async () => {
      const sandboxId = 'test-sandbox-123';

      mockDaytonaService.destroySandbox.mockResolvedValue();

      const result = await service.destroySandbox(sandboxId);

      expect(result).toBeUndefined();
      expect(mockDaytonaService.destroySandbox).toHaveBeenCalledWith(sandboxId);
    });
  });

  describe('checkPreviewHealth', () => {
    it('should return healthy status', async () => {
      const previewUrl = 'https://test-sandbox.daytona.dev';

      const mockHealthResult: DaytonaHealthCheckResult = {
        status: 'healthy',
        message: 'Preview is healthy and ready',
        timestamp: '2023-01-01T00:00:00.000Z',
      };

      mockDaytonaService.healthCheck.mockResolvedValue(mockHealthResult);

      const result = await service.checkPreviewHealth(previewUrl);

      expect(result).toEqual(mockHealthResult);
      expect(mockDaytonaService.healthCheck).toHaveBeenCalledWith(previewUrl);
    });

    it('should return unhealthy status', async () => {
      const previewUrl = 'https://test-sandbox.daytona.dev';

      const mockHealthResult: DaytonaHealthCheckResult = {
        status: 'unhealthy',
        message: 'Health check failed: Connection timeout',
        timestamp: '2023-01-01T00:00:00.000Z',
      };

      mockDaytonaService.healthCheck.mockResolvedValue(mockHealthResult);

      const result = await service.checkPreviewHealth(previewUrl);

      expect(result).toEqual(mockHealthResult);
      expect(mockDaytonaService.healthCheck).toHaveBeenCalledWith(previewUrl);
    });
  });
});
