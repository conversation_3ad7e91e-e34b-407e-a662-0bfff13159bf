import { Injectable, Logger } from '@nestjs/common';
import { Daytona } from '@daytonaio/sdk';

export interface DaytonaSandboxResult {
  sandboxId: string;
  previewUrl: string;
  previewToken: string;
  filesCount?: number;
  provider: 'daytona';
  message: string;
}

export interface DaytonaUploadResult {
  success: boolean;
  filesCount: number;
  message: string;
}

export interface DaytonaHealthCheckResult {
  status: 'healthy' | 'unhealthy';
  message: string;
  timestamp: string;
}

export interface DaytonaLogsResult {
  success: boolean;
  logs: string;
  timestamp: string;
  message: string;
}

@Injectable()
export class DaytonaService {
  private readonly logger = new Logger(DaytonaService.name);
  private daytona: Daytona;

  constructor() {
    this.daytona = new Daytona();
    this.logger.log('DaytonaService initialized');
  }

  /**
   * Create a new Daytona sandbox
   */
  async createSandbox(): Promise<DaytonaSandboxResult> {
    this.logger.log('Creating Daytona sandbox...');

    const THREE_MINUTES = 3;
    const SNAPTSHOT = 'fullstackfox';
    const PORT = 3000;
    const PUBLIC = true;
    const AUTO_STOP_INTERVAL = THREE_MINUTES;
    const AUTO_DELETE_INTERVAL = THREE_MINUTES;

    try {
      const sandbox = await this.daytona.create({
        snapshot: SNAPTSHOT,
        autoStopInterval: AUTO_STOP_INTERVAL,
        autoDeleteInterval: AUTO_DELETE_INTERVAL,
        public: PUBLIC,
      });

      this.logger.log(`Sandbox created: ${sandbox.id}`);

      // Get preview link
      const previewInfo = await sandbox.getPreviewLink(PORT);
      this.logger.log(`Preview link generated: ${previewInfo.url}`);

      return {
        sandboxId: sandbox.id,
        previewUrl: previewInfo.url,
        previewToken: previewInfo.token,
        provider: 'daytona',
        message: 'Daytona sandbox created successfully',
      };
    } catch (error) {
      this.logger.error('Failed to create Daytona sandbox:', error);
      throw new Error(`Sandbox creation failed: ${error.message}`);
    }
  }

  /**
   * Destroy a Daytona sandbox
   */
  async destroySandbox(sandboxId: string): Promise<void> {
    this.logger.log(`Destroying sandbox: ${sandboxId}`);

    try {
      const sandbox = await this.daytona.findOne({ id: sandboxId });
      if (sandbox) {
        await sandbox.delete();
        this.logger.log(`Sandbox ${sandboxId} destroyed successfully`);
      } else {
        this.logger.warn(`Sandbox ${sandboxId} not found`);
      }
    } catch (error) {
      this.logger.error(`Failed to destroy sandbox ${sandboxId}:`, error);
      // Don't throw error - cleanup failures shouldn't break the flow
    }
  }

  /**
   * Upload files to an existing sandbox
   */
  async uploadFiles(
    sandboxId: string,
    files: Record<string, string>,
  ): Promise<DaytonaUploadResult> {
    this.logger.log(`Uploading files to sandbox: ${sandboxId}`);

    try {
      const sandbox = await this.daytona.findOne({ id: sandboxId });
      if (!sandbox) {
        throw new Error(`Sandbox ${sandboxId} not found`);
      }

      // Convert Record<string, string> to the required format
      const uploadFiles = Object.entries(files).map(
        ([destination, content]) => {
          const contentString =
            typeof content === 'string' ? content : String(content);
          return {
            source: Buffer.from(contentString, 'utf8'),
            destination: destination,
          };
        },
      );

      await sandbox.fs.uploadFiles(uploadFiles);
      this.logger.log(`Uploaded ${uploadFiles.length} files to sandbox`);

      return {
        success: true,
        filesCount: uploadFiles.length,
        message: 'Files uploaded successfully',
      };
    } catch (error) {
      this.logger.error(
        `Failed to upload files to sandbox ${sandboxId}:`,
        error,
      );
      throw new Error(`File upload failed: ${error.message}`);
    }
  }

  /**
   * Health check for a preview URL
   */
  async healthCheck(previewUrl: string): Promise<DaytonaHealthCheckResult> {
    this.logger.log(`Health check for: ${previewUrl}`);

    try {
      const response = await fetch(`${previewUrl}/api/health`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000), // 5 second timeout
      });

      if (response.ok) {
        return {
          status: 'healthy',
          message: 'Preview is healthy and ready',
          timestamp: new Date().toISOString(),
        };
      } else {
        return {
          status: 'unhealthy',
          message: `Health check failed with status: ${response.status}`,
          timestamp: new Date().toISOString(),
        };
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        message: `Health check failed: ${error.message}`,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Health check with polling and timeout
   */
  async healthCheckWithTimeout(
    previewUrl: string,
    timeoutMs: number = 15000,
  ): Promise<void> {
    const startTime = Date.now();
    const pollInterval = 500; // 500ms

    while (Date.now() - startTime < timeoutMs) {
      const result = await this.healthCheck(previewUrl);

      if (result.status === 'healthy') {
        this.logger.log(`Health check passed for ${previewUrl}`);
        return;
      }

      // Wait before next poll
      await new Promise((resolve) => setTimeout(resolve, pollInterval));
    }

    throw new Error(`Health check timeout after ${timeoutMs}ms`);
  }

  /**
   * Get logs from a Daytona sandbox
   * Retrieves logs from the main dev server session
   */
  async getLogs(sandboxId: string): Promise<DaytonaLogsResult> {
    this.logger.log(`Getting logs for sandbox: ${sandboxId}`);

    try {
      const sandbox = await this.daytona.findOne({ id: sandboxId });
      if (!sandbox) {
        throw new Error(`Sandbox ${sandboxId} not found`);
      }

      const sessionId = 'dev-server-session';
      let logs = '';

      try {
        // Try to get logs from existing dev server session
        // This assumes the dev server is running in a session called 'dev-server-session'
        await sandbox.process.getSessionCommandLogs(
          sessionId,
          undefined, // Get logs from all commands in the session
          (chunk) => {
            // Clean the chunk and append to logs
            const cleanChunk = chunk
              .replace(/\x00/g, '')
              .replace(/\r\n/g, '\n');
            logs += cleanChunk;
          },
        );

        // Get only the last 10 seconds worth of logs (approximate)
        const logLines = logs.split('\n');
        const recentLines = logLines.slice(-50); // Get last 50 lines as approximation
        const recentLogs = recentLines.join('\n');

        return {
          success: true,
          logs: recentLogs || 'No recent logs available',
          timestamp: new Date().toISOString(),
          message: 'Logs retrieved successfully',
        };
      } catch (sessionError) {
        // If no session exists, try to create one and run a command to get system logs
        this.logger.warn(
          `No existing session found, creating new one: ${sessionError.message}`,
        );

        try {
          await sandbox.process.createSession(sessionId);

          // Execute a command to get recent logs from the system
          const command = await sandbox.process.executeSessionCommand(
            sessionId,
            {
              command:
                'tail -n 50 /tmp/dev-server.log 2>/dev/null || echo "No dev server logs found"',
              async: false,
            },
          );

          return {
            success: true,
            logs: command.output || 'No logs available',
            timestamp: new Date().toISOString(),
            message: 'System logs retrieved successfully',
          };
        } catch (commandError) {
          this.logger.error(
            `Failed to get logs via command: ${commandError.message}`,
          );

          return {
            success: false,
            logs: '',
            timestamp: new Date().toISOString(),
            message: `No logs available: ${commandError.message}`,
          };
        }
      }
    } catch (error) {
      this.logger.error(`Failed to get logs for sandbox ${sandboxId}:`, error);
      throw new Error(`Failed to get logs: ${error.message}`);
    }
  }
}
