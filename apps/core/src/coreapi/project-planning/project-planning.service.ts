import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { SupabaseService } from '@shared-library/modules/supabase/supabase.service';
import { generateObject } from 'ai';
import { PrismaService } from 'src/persistence/prisma/prisma.service';
import { ai_models } from 'src/utils/models';
import { getBlueprintPrompt, getModuleDetailPrompt } from 'src/utils/prompts';
import { GenerateProjectPlanDto } from './dto/project-planning.dto';
import {
  ModuleDetail,
  ModuleDetailSchema,
} from './schemas/module-detail-schema';
import {
  ProjectBlueprint,
  ProjectBlueprintSchema,
} from './schemas/project-blueprint-schema';

@Injectable()
export class ProjectPlanningService {
  constructor(
    private prisma: PrismaService,
    private supabaseService: SupabaseService,
  ) {}

  // Phase 1: Generate app-specific blueprint (10-30 seconds)
  async generateProjectBlueprint({
    message,
    workspaceId,
  }: GenerateProjectPlanDto) {
    try {
      const blueprint = await generateObject({
        model: ai_models.deepseek['deepseek-r1-0528'],
        prompt: message,
        system: getBlueprintPrompt(), // Updated prompt
        schema: ProjectBlueprintSchema,
        maxTokens: 64000, // Reduced for blueprint
        providerOptions: {
          openrouter: {
            provider: {
              sort: 'throughput',
              order: ['fireworks'],
            },
          },
        },
      });

      // Save blueprint to existing table
      await this.prisma.projectPlan.upsert({
        where: { workspaceId },
        update: { data: blueprint.object as unknown as ProjectBlueprint },
        create: {
          workspaceId,
          data: blueprint.object as unknown as ProjectBlueprint,
        },
      });

      return blueprint.object as ProjectBlueprint;
    } catch (error) {
      console.error('Project blueprint generation error:', error);
      throw new Error(
        'Failed to generate project blueprint: ' +
          (error.message || 'Unknown error'),
      );
    }
  }

  // Phase 2: Generate detailed module for Next.js implementation
  async generateModuleDetails(moduleId: string, workspaceId: string) {
    try {
      const projectPlan = await this.getProjectPlan(workspaceId);
      if (!projectPlan || !projectPlan.data) {
        throw new Error('Project plan not found or blueprint data missing.');
      }
      const blueprint = projectPlan.data as unknown as ProjectBlueprint;
      const selectedModule = blueprint.modules.find(
        (module) => module.id === moduleId,
      );
      const selectedModuleTitle = selectedModule?.title;

      const moduleDetails = await generateObject({
        model: ai_models.deepseek['deepseek-r1-0528'],
        prompt: `Generate Next.js App Router implementation for module: ${moduleId}.`,
        system: getModuleDetailPrompt(blueprint, selectedModuleTitle),
        schema: ModuleDetailSchema,
        maxTokens: 64000, // Reduced for blueprint
      });

      // Save to ProjectModule table
      const projectModule = await this.prisma.projectModule.upsert({
        where: {
          projectPlanId_moduleId: {
            projectPlanId: projectPlan.id,
            moduleId,
          },
        },
        update: {
          data: moduleDetails.object as unknown as ModuleDetail,
          status: 'detailed',
        },
        create: {
          projectPlanId: projectPlan.id,
          moduleId,
          data: moduleDetails.object as unknown as ModuleDetail,
          status: 'detailed',
        },
      });

      // Extract and save pages to ProjectPage table
      if (
        moduleDetails.object &&
        (moduleDetails.object as ModuleDetail).pages
      ) {
        const pages = (moduleDetails.object as ModuleDetail).pages;

        // Delete existing pages for this module to avoid duplicates
        await this.prisma.projectPage.deleteMany({
          where: { projectModuleId: projectModule.id },
        });

        // Create new pages
        for (const page of pages) {
          await this.prisma.projectPage.create({
            data: {
              projectModuleId: projectModule.id,
              pageId: page.id,
              title: page.title,
              status: page.status,
              description: page.description || '',
              pageType: page.pageType,
              layoutId: page.layoutId,
              route: page.route,
              isClientComponent: page.isClientComponent || false,
              additionalData: page as unknown as Prisma.JsonObject,
            },
          });
        }
      }

      return moduleDetails.object as ModuleDetail;
    } catch (error) {
      console.error('Module detail generation error:', error);
      throw new Error(
        'Failed to generate module details: ' +
          (error.message || 'Unknown error'),
      );
    }
  }

  async getProjectPlan(workspaceId: string) {
    return this.prisma.projectPlan.findUnique({
      where: { workspaceId: workspaceId },
    });
  }

  // Workflow support methods
  async getProjectModules(workspaceId: string) {
    const project = await this.prisma.projectPlan.findUnique({
      where: { workspaceId },
      select: {
        id: true,
        data: true,
      },
    });

    if (!project) {
      return [];
    }

    // Ensure project.data is treated as ProjectBlueprint
    const blueprint = project?.data as unknown as ProjectBlueprint | undefined;
    const modules = blueprint?.modules || [];

    // Get module status information from ProjectModule table
    const projectModules = await this.prisma.projectModule.findMany({
      where: { projectPlanId: project.id },
      select: {
        moduleId: true,
        status: true,
      },
    });

    // Create a map for quick lookup
    const statusMap = new Map(
      projectModules.map((pm) => [pm.moduleId, pm.status]),
    );

    // Add status to each module
    const modulesWithStatus = modules.map((module) => ({
      ...module,
      status: statusMap.get(module.id) || 'planned', // Default to 'planned' if not found
    }));

    return modulesWithStatus;
  }

  async getModulePages(moduleId: string, workspaceId: string) {
    // First try to get pages from the ProjectPage table
    const projectPlan = await this.prisma.projectPlan.findUnique({
      where: { workspaceId },
      select: { id: true },
    });

    if (!projectPlan) {
      return [];
    }

    const projectModule = await this.prisma.projectModule.findUnique({
      where: {
        projectPlanId_moduleId: {
          projectPlanId: projectPlan.id,
          moduleId,
        },
      },
      select: { id: true },
    });

    if (!projectModule) {
      return [];
    }

    const pages = await this.prisma.projectPage.findMany({
      where: { projectModuleId: projectModule.id },
    });

    if (pages.length > 0) {
      return pages;
    }

    // Fallback to the existing method if no pages found in ProjectPage table
    const moduleDetailsData = await this.getModuleDetailsData(
      moduleId,
      workspaceId,
    );
    const moduleDetails = moduleDetailsData?.data as unknown as
      | ModuleDetail
      | undefined;
    return moduleDetails?.pages || [];
  }

  async getPageStories(moduleId: string, pageId: string, workspaceId: string) {
    const moduleDetailsData = await this.getModuleDetailsData(
      moduleId,
      workspaceId,
    );
    const moduleDetails = moduleDetailsData?.data as unknown as
      | ModuleDetail
      | undefined;
    const stories = moduleDetails?.stories || [];

    return {
      page: moduleDetails?.pages.find((p) => p.id === pageId) || null,
      // layouts: moduleDetails?.layouts || [],
      stories:
        stories.filter(
          (story) => story.parentId === pageId || story.pageId === pageId,
        ) || [],
    };
  }

  private async getModuleDetailsData(moduleId: string, workspaceId: string) {
    const project = await this.prisma.projectPlan.findUnique({
      where: { workspaceId },
      select: { id: true }, // Select only the id
    });
    if (!project) {
      return null;
    }
    return await this.prisma.projectModule.findUnique({
      where: {
        projectPlanId_moduleId: {
          projectPlanId: project.id,
          moduleId,
        },
      },
      select: { data: true }, // Select only the data field
    });
  }

  async getPage(moduleId: string, pageId: string, workspaceId: string) {
    const projectPlan = await this.prisma.projectPlan.findUnique({
      where: { workspaceId },
      select: { id: true },
    });

    if (!projectPlan) {
      return null;
    }

    const projectModule = await this.prisma.projectModule.findUnique({
      where: {
        projectPlanId_moduleId: {
          projectPlanId: projectPlan.id,
          moduleId,
        },
      },
      select: { id: true },
    });

    if (!projectModule) {
      return null;
    }

    const page = await this.prisma.projectPage.findUnique({
      where: {
        projectModuleId_pageId: {
          projectModuleId: projectModule.id,
          pageId,
        },
      },
    });

    if (page) {
      return page;
    }

    const moduleDetailsData = await this.getModuleDetailsData(
      moduleId,
      workspaceId,
    );
    const moduleDetails = moduleDetailsData?.data as unknown as
      | ModuleDetail
      | undefined;
    return moduleDetails?.pages.find((p) => p.id === pageId) || null;
  }

  async getPageEntity(workspaceId: string, moduleId: string, pageId: string) {
    const projectPlan = await this.prisma.projectPlan.findUnique({
      where: { workspaceId },
      select: { id: true },
    });

    if (!projectPlan) {
      return null;
    }

    const projectModule = await this.prisma.projectModule.findUnique({
      where: {
        projectPlanId_moduleId: {
          projectPlanId: projectPlan.id,
          moduleId,
        },
      },
      select: { id: true },
    });

    if (!projectModule) {
      return null;
    }

    return this.prisma.projectPage.findUnique({
      where: {
        projectModuleId_pageId: {
          projectModuleId: projectModule.id,
          pageId,
        },
      },
    });
  }

  async saveChatMessage(
    workspaceId: string,
    moduleId: string,
    pageId: string,
    userId: number,
    message: string,
    role: string,
  ) {
    const page = await this.getPageEntity(workspaceId, moduleId, pageId);

    if (!page) {
      throw new Error('Page not found');
    }

    return this.prisma.pageChatHistory.create({
      data: {
        projectPageId: page.id,
        userId,
        message,
        role,
      },
    });
  }

  async getChatHistory(workspaceId: string, moduleId: string, pageId: string) {
    const page = await this.getPageEntity(workspaceId, moduleId, pageId);

    if (!page) {
      return [];
    }

    return this.prisma.pageChatHistory.findMany({
      where: { projectPageId: page.id },
      orderBy: { createdAt: 'asc' },
    });
  }

  async migrateModuleSQL(moduleId: string, workspaceId: string) {
    try {
      // 1. Get project plan
      const projectPlan = await this.getProjectPlan(workspaceId);
      if (!projectPlan) {
        throw new Error('Project plan not found');
      }

      // 2. Get project module
      const projectModule = await this.prisma.projectModule.findUnique({
        where: {
          projectPlanId_moduleId: {
            projectPlanId: projectPlan.id,
            moduleId,
          },
        },
      });

      if (!projectModule) {
        throw new Error('Module not found');
      }

      // 3. Check if already migrated
      if (projectModule.status === 'sql_migrated') {
        return {
          success: true,
          message: 'SQL migration already completed',
          alreadyMigrated: true,
        };
      }

      // 4. Extract SQL from module data
      const moduleDetails = projectModule.data as unknown as ModuleDetail;
      const completeSupabaseSQL = moduleDetails?.completeSupabaseSQL;

      if (!completeSupabaseSQL) {
        throw new Error('No SQL migration found in module details');
      }

      // 5. Execute migration via SupabaseService
      await this.supabaseService.migrate(workspaceId, completeSupabaseSQL);

      // 6. Update module status to 'sql_migrated'
      await this.prisma.projectModule.update({
        where: {
          projectPlanId_moduleId: {
            projectPlanId: projectPlan.id,
            moduleId,
          },
        },
        data: {
          status: 'sql_migrated',
        },
      });

      return {
        success: true,
        message: 'SQL migration completed successfully',
        alreadyMigrated: false,
      };
    } catch (error) {
      console.error('Module SQL migration error:', error);
      throw new Error(
        'Failed to migrate module SQL: ' + (error.message || 'Unknown error'),
      );
    }
  }
}
