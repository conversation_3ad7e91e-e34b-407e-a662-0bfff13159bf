# Database Page Implementation Plan

## Overview

Create a new "Database" page in the admin frontend to display Supabase public schema tables with their columns and data. The page will have a sidebar showing table names and a main content area displaying the selected table's data using TanStack Table.

## Current Architecture Analysis

### Frontend Structure

- **Routing**: Uses React Router with lazy-loaded components in `apps/admin/src/config/routes.tsx`
- **Layout**: Dashboard layout with sidebar (`DashboardLayout`) and header (`DashboardHeader`)
- **Navigation**: Header has Project Overview and Integration tabs
- **Styling**: Dark theme (`#0f1114` background) with shadcn/ui components
- **State Management**: Zustand store (`useBaseStore`) for workspace management
- **API Client**: Axios-based client with TanStack Query for data fetching

### Backend Structure

- **Supabase Service**: `apps/core/libs/shared/src/modules/supabase/supabase.service.ts`
- **Migration Function**: Already exists and can execute SQL queries
- **Connection Model**: `WorkspaceSupabaseConnection` stores project details and tokens
- **Database Access**: Uses `pg` Client with connection pooler

### TanStack Table Setup

- **Components**: Already has `DataTable` component in `apps/admin/src/features/home/<USER>
- **UI Components**: Table components available in `@/components/ui/table`
- **Styling**: Dark theme compatible with existing design

## Implementation Steps

### Step 1: Backend API Development

#### 1.1 Create Database API Endpoints

**File**: `apps/core/libs/shared/src/modules/supabase/supabase.controller.ts`

Add new endpoints:

- `GET /supabase/tables/:workspaceId` - Get all public tables
- `GET /supabase/table-structure/:workspaceId/:tableName` - Get table columns
- `GET /supabase/table-data/:workspaceId/:tableName` - Get table data

#### 1.2 Extend Supabase Service

**File**: `apps/core/libs/shared/src/modules/supabase/supabase.service.ts`

Add methods:

```typescript
async getPublicTables(workspaceId: string): Promise<TableInfo[]>
async getTableStructure(workspaceId: string, tableName: string): Promise<ColumnInfo[]>
async getTableData(workspaceId: string, tableName: string): Promise<any[]>
```

#### 1.3 SQL Queries Implementation

```sql
-- Get public tables
SELECT table_name, table_type
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_type = 'BASE TABLE'
ORDER BY table_name;

-- Get table columns
SELECT
    column_name,
    data_type,
    is_nullable,
    column_default,
    ordinal_position
FROM information_schema.columns
WHERE table_schema = 'public'
AND table_name = $1
ORDER BY ordinal_position;

-- Get table data (with pagination)
SELECT * FROM public."${tableName}" LIMIT 100;
```

### Step 2: Frontend API Integration

#### 2.1 Create Database API Client

**File**: `apps/admin/src/features/database/api/database-api.ts`

```typescript
import { api } from '@/lib/api-client';
import { useBaseStore } from '@/store/base-store';
import { useQuery } from '@tanstack/react-query';

export type TableInfo = {
  table_name: string;
  table_type: string;
};

export type ColumnInfo = {
  column_name: string;
  data_type: string;
  is_nullable: string;
  column_default: string | null;
  ordinal_position: number;
};

// API functions
const getTables = async (workspaceId: string): Promise<TableInfo[]> => {
  return api.get(`/supabase/tables/${workspaceId}`);
};

const getTableStructure = async (
  workspaceId: string,
  tableName: string,
): Promise<ColumnInfo[]> => {
  return api.get(`/supabase/table-structure/${workspaceId}/${tableName}`);
};

const getTableData = async (
  workspaceId: string,
  tableName: string,
): Promise<any[]> => {
  return api.get(`/supabase/table-data/${workspaceId}/${tableName}`);
};

// Hooks
export const useTables = () => {
  const { workspaceId } = useBaseStore();
  return useQuery({
    queryKey: ['database-tables', workspaceId],
    queryFn: () => getTables(workspaceId as string),
    enabled: !!workspaceId,
  });
};

export const useTableStructure = (tableName: string | null) => {
  const { workspaceId } = useBaseStore();
  return useQuery({
    queryKey: ['table-structure', workspaceId, tableName],
    queryFn: () =>
      getTableStructure(workspaceId as string, tableName as string),
    enabled: !!workspaceId && !!tableName,
  });
};

export const useTableData = (tableName: string | null) => {
  const { workspaceId } = useBaseStore();
  return useQuery({
    queryKey: ['table-data', workspaceId, tableName],
    queryFn: () => getTableData(workspaceId as string, tableName as string),
    enabled: !!workspaceId && !!tableName,
  });
};
```

### Step 3: UI Components Development

#### 3.1 Database Page Layout

**File**: `apps/admin/src/features/database/main.tsx`

```typescript
import DashboardHeader from '@/components/shared/DashboardHeader';
import { useState } from 'react';
import { DatabaseSidebar } from './components/database-sidebar';
import { TableViewer } from './components/table-viewer';
import { useTables } from './api/database-api';

export default function DatabasePage() {
  const [selectedTable, setSelectedTable] = useState<string | null>(null);
  const { data: tables, isLoading } = useTables();

  return (
    <div className="flex flex-col h-screen bg-[#0f1114] text-white">
      <DashboardHeader />
      <div className="flex-1 flex">
        <DatabaseSidebar
          tables={tables || []}
          selectedTable={selectedTable}
          onTableSelect={setSelectedTable}
          isLoading={isLoading}
        />
        <div className="flex-1">
          <TableViewer selectedTable={selectedTable} />
        </div>
      </div>
    </div>
  );
}
```

#### 3.2 Database Sidebar Component

**File**: `apps/admin/src/features/database/components/database-sidebar.tsx`

```typescript
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { Database } from 'lucide-react';
import { TableInfo } from '../api/database-api';

interface DatabaseSidebarProps {
  tables: TableInfo[];
  selectedTable: string | null;
  onTableSelect: (tableName: string) => void;
  isLoading: boolean;
}

export function DatabaseSidebar({ tables, selectedTable, onTableSelect, isLoading }: DatabaseSidebarProps) {
  return (
    <div className="w-64 bg-[#090D12] border-r border-gray-800 flex flex-col">
      <div className="p-4 border-b border-gray-800">
        <div className="flex items-center gap-2">
          <Database className="w-5 h-5 text-orange-500" />
          <h2 className="text-lg font-semibold text-white">Database Tables</h2>
        </div>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-2">
          {isLoading ? (
            <div className="space-y-2">
              {Array.from({ length: 5 }).map((_, i) => (
                <Skeleton key={i} className="h-8 w-full bg-gray-800" />
              ))}
            </div>
          ) : (
            <div className="space-y-1">
              {tables.map((table) => (
                <button
                  key={table.table_name}
                  onClick={() => onTableSelect(table.table_name)}
                  className={`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                    selectedTable === table.table_name
                      ? 'bg-orange-500/20 text-orange-400 border border-orange-500/30'
                      : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                  }`}
                >
                  {table.table_name}
                </button>
              ))}
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
}
```

#### 3.3 Table Viewer Component

**File**: `apps/admin/src/features/database/components/table-viewer.tsx`

```typescript
import { DataTable } from '@/components/shared/data-table';
import { Skeleton } from '@/components/ui/skeleton';
import { ColumnDef } from '@tanstack/react-table';
import { useMemo } from 'react';
import { useTableData, useTableStructure } from '../api/database-api';

interface TableViewerProps {
  selectedTable: string | null;
}

export function TableViewer({ selectedTable }: TableViewerProps) {
  const { data: structure, isLoading: structureLoading } = useTableStructure(selectedTable);
  const { data: tableData, isLoading: dataLoading } = useTableData(selectedTable);

  const columns: ColumnDef<any>[] = useMemo(() => {
    if (!structure) return [];

    return structure.map((column) => ({
      accessorKey: column.column_name,
      header: column.column_name,
      cell: ({ row }) => {
        const value = row.getValue(column.column_name);
        return (
          <div className="text-sm text-gray-300">
            {value === null ? (
              <span className="text-gray-500 italic">null</span>
            ) : (
              String(value)
            )}
          </div>
        );
      },
    }));
  }, [structure]);

  if (!selectedTable) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center text-gray-400">
          <Database className="w-16 h-16 mx-auto mb-4 opacity-50" />
          <p className="text-lg">Select a table to view its data</p>
        </div>
      </div>
    );
  }

  if (structureLoading || dataLoading) {
    return (
      <div className="p-6">
        <Skeleton className="h-8 w-48 mb-4 bg-gray-800" />
        <div className="space-y-2">
          {Array.from({ length: 10 }).map((_, i) => (
            <Skeleton key={i} className="h-12 w-full bg-gray-800" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-white mb-2">{selectedTable}</h1>
        <p className="text-gray-400">
          {tableData?.length || 0} rows • {structure?.length || 0} columns
        </p>
      </div>

      <div className="bg-[#1a1d23] rounded-lg border border-gray-800">
        <DataTable columns={columns} data={tableData || []} />
      </div>
    </div>
  );
}
```

### Step 4: Routing and Navigation

#### 4.1 Add Database Route

**File**: `apps/admin/src/config/routes.tsx`

Add to routes array:

```typescript
const Database = lazy(() => import('@/features/database/main'));

// In children array:
{ path: 'database', element: <Database /> },
```

#### 4.2 Update Dashboard Header

**File**: `apps/admin/src/components/shared/DashboardHeader.tsx`

Add database tab to header navigation alongside Project Overview and Integration.

### Step 5: Error Handling and Edge Cases

#### 5.1 Connection Validation

- Check if Supabase connection exists before making database calls
- Show appropriate error messages for missing connections
- Handle connection timeouts and database errors gracefully

#### 5.2 Empty States

- No tables found
- No data in selected table
- Connection not configured

#### 5.3 Data Type Handling

- Handle different PostgreSQL data types (JSON, arrays, etc.)
- Format dates and timestamps appropriately
- Handle large text fields with truncation

### Step 6: Performance Considerations

#### 6.1 Pagination

- Implement pagination for large tables
- Add row limit controls (50, 100, 500 rows)

#### 6.2 Caching

- Use TanStack Query caching for table lists
- Implement stale-while-revalidate for table data

#### 6.3 Virtual Scrolling

- Consider virtual scrolling for tables with many columns
- Optimize rendering for large datasets

## File Structure

```
apps/admin/src/features/database/
├── main.tsx                    # Main database page component
├── api/
│   └── database-api.ts         # API client and hooks
├── components/
│   ├── database-sidebar.tsx    # Left sidebar with table list
│   ├── table-viewer.tsx        # Main table display area
│   └── table-data-table.tsx    # TanStack table wrapper
└── types/
    └── database-types.ts       # TypeScript type definitions
```

## Dependencies

- All required dependencies are already installed:
  - `@tanstack/react-table` ✅
  - `@tanstack/react-query` ✅
  - `shadcn/ui` components ✅
  - `lucide-react` icons ✅

## Testing Strategy

1. Test with different table structures (empty tables, tables with various data types)
2. Test error scenarios (no connection, connection timeout)
3. Test performance with large datasets
4. Test responsive design on different screen sizes

## Security Considerations

- Use parameterized queries to prevent SQL injection
- Limit data exposure (consider row limits)
- Validate table names before querying
- Ensure proper authentication for database access
