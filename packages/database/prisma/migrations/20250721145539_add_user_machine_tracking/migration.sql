-- CreateTable
CREATE TABLE `user_machines` (
    `id` VARCHAR(191) NOT NULL,
    `userId` INTEGER NOT NULL,
    `appName` VARCHAR(191) NOT NULL,
    `machineId` VARCHAR(191) NOT NULL,
    `status` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `user_machines` ADD CONSTRAINT `user_machines_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
