# Live Preview Optimization Plan

## Overview

Optimize the live preview system to reduce startup time from 30-40 seconds by implementing persistent apps per user and machine suspension instead of deletion.

## Health Check Types (Updated and Clarified)

### 1. **Machine Infrastructure Health Check** (`checkMachineStatus`)

- **Purpose**: Verify the Fly machine itself is started and running at the infrastructure level
- **Implementation**: Uses Fly's `/wait` API endpoint with state='started'
- **Speed**: Fast (usually immediate, max 30 seconds)
- **Use Cases**:
  - Creating new machines
  - Reactivating suspended machines
  - Verifying machine state before operations

### 2. **Application Health Check** (`checkApplicationHealth`)

- **Purpose**: Verify the application inside the machine is responding to HTTP requests
- **Implementation**: HTTP requests to the app's `/api/health` endpoint
- **Speed**: Slower (can take 30+ seconds for cold application starts)
- **Use Cases**:
  - After code injection
  - Verifying app is ready to serve user requests
  - Before performing application-level operations

### 3. **Comprehensive Health Check** (`performFullHealthCheck`)

- **Purpose**: Combines both checks in sequence (machine + application)
- **Use Cases**: Complete verification when creating new machines

## Current System Analysis

- **Current flow**: Create app → Create machine → Delete entire app after 2 minutes
- **Problem**: 30-40 second startup time on every preview
- **Solution**: Persistent apps + machine suspension/reactivation

## Implementation Plan

### Phase 1: User App Creation on Signup

#### 1.1 Modify User Registration Flow

- **Location**: `apps/core/src/coreapi/auth/auth.service.ts` - in the user signup/registration method
- **Action**: Add fly app creation to user registration
- **Context**: Modify the existing user registration flow to create a persistent Fly app for each new user
- **Implementation**:

  ```typescript
  import { v4 as uuidv4 } from 'uuid';

  // Add to user registration process
  const appName = uuidv4(); // Use UUID for unique app names
  await flyPreviewManager.createApp(appName);
  // Store app name in user record for future reference
  ```

#### 1.2 Database Schema Update

- **Location**: `packages/database/prisma/schema/user.prisma` - Add field to existing User model
- **Action**: Add `fly_app_name` field to User model
- **Context**: Update the User model to store each user's persistent Fly app name
- **Implementation**:
  ```prisma
  model User {
    // ... existing fields
    fly_app_name String? // Store the fly app name
  }
  ```

#### 1.3 User Deletion Flow

- **Location**: User deletion service
- **Action**: Delete fly app when user account is deleted
- **Implementation**:
  ```typescript
  // On user deletion
  await flyPreviewManager.destroyApp(user.fly_app_name);
  ```

### Phase 2: Machine Management Optimization

#### 2.1 Modify Preview Creation

- **Location**: `apps/core/src/preview/fly-preview-manager.ts`
- **Action**: Separate app creation from machine creation
- **Changes**:
  - Create new method `createMachineInExistingApp(appName: string)`
  - Modify `createPreviewApp()` to use existing app
  - Remove app creation logic from preview flow

#### 2.2 Implement Machine Suspension

- **Location**: `apps/core/src/preview/fly-preview-manager.ts`
- **Action**: Add machine suspension method
- **Implementation**:
  ```typescript
  async suspendMachine(appName: string, machineId: string): Promise<void> {
    return this.makeRequest(
      `/v1/apps/${appName}/machines/${machineId}/suspend`,
      'POST'
    );
  }
  ```

#### 2.3 Implement Machine Reactivation

- **Location**: `apps/core/src/preview/fly-preview-manager.ts`
- **Action**: Add machine start method
- **Implementation**:
  ```typescript
  async startMachine(appName: string, machineId: string): Promise<void> {
    return this.makeRequest(
      `/v1/apps/${appName}/machines/${machineId}/start`,
      'POST'
    );
  }
  ```

#### 2.4 Get User Machine

- **Location**: `apps/core/src/preview/fly-preview-manager.ts`
- **Action**: Add method to get user's machine for an app
- **Implementation**:
  ```typescript
  async getUserMachine(appName: string): Promise<FlyMachine | null> {
    const machines = await this.makeRequest<FlyMachine[]>(`/v1/apps/${appName}/machines`);
    return machines.length > 0 ? machines[0] : null; // Single machine per user
  }
  ```

### Phase 3: Backend API Endpoints

#### 3.1 Machine Suspension API

- **Location**: `apps/core/src/preview/preview.controller.ts` - Add new POST endpoint method
- **Action**: Create suspension endpoint
- **Context**: Add to existing PreviewController class for machine management
- **Implementation**:
  ```typescript
  @Post('suspend/:appName/:machineId')
  async suspendMachine(
    @Param('appName') appName: string,
    @Param('machineId') machineId: string
  ) {
    return this.previewService.suspendMachine(appName, machineId);
  }
  ```

#### 3.2 Health Check & Reactivation API

- **Location**: `apps/core/src/preview/preview.controller.ts` - Add new POST endpoint method
- **Action**: Create health check with auto-reactivation
- **Context**: Add to existing PreviewController class for background machine preparation
- **Implementation**:
  ```typescript
  @Post('health-check/:appName')
  async healthCheckAndReactivate(@Param('appName') appName: string) {
    return this.previewService.healthCheckAndReactivate(appName);
  }
  ```

#### 3.3 Cleanup API (Logout)

- **Location**: `apps/core/src/preview/preview.controller.ts` - Add new DELETE endpoint method
- **Action**: Create cleanup endpoint for user logout
- **Context**: **✅ ALREADY IMPLEMENTED** - Add to existing PreviewController class, called from sidebar logout button
- **Implementation**:
  ```typescript
  @Delete('cleanup')
  async cleanupUserMachine(@Request() req) {
    const userId = req.user.id; // Get user ID from auth token
    return this.previewService.cleanupUserMachine(userId);
  }
  ```

### Phase 4: Service Layer Updates

#### 4.1 Preview Service Modifications

- **Location**: `apps/core/src/preview/preview.service.ts`
- **Action**: Implement new machine management logic
- **Key Methods**:
  - `createMachineForUser()`: **Check for suspended machine first, reactivate if found, create new only if none exist**
- `createPreviewForUser()`: **Just do health check + code injection (fast operation <5s)**
- `healthCheckAndReactivate()`: Check if machine is suspended, reactivate if needed
- `suspendMachine()`: Suspend machine after timeout
- `cleanupUserMachine()`: Delete machine for user

#### 4.2 Enhanced Health Check Logic (for createMachineForUser)

- **Logic Flow** (SINGLE MACHINE APPROACH):
  1. **Check database for user's current machine**
  2. **If no machine exists, create new one** (30-40s)
  3. **If machine exists, check actual state on Fly**:
     - **If suspended, call start API** ⚡ **(5-10s startup)**
     - **If running, use it immediately** ⚡ **(0s startup)**
     - **If broken/missing, replace with new one** (30-40s)

#### 4.3 Optimized Preview Creation (for createPreviewForUser)

- **Logic Flow** (happens during onFinish):
  1. **Quick health check only** (machine should already be ready from background)
  2. **Get machine ID from database** (single lookup, no arrays)
  3. **Inject code into running machine** (fast operation)
  4. **Schedule suspension after 2 minutes**

#### 4.4 Correct API Flow

**Background Call** (`/preview/create-machine` → `createMachineForUser`):

- ✅ Check for suspended machine
- ✅ Reactivate suspended machine (5-10s)
- ✅ Create new machine only if none exist (30-40s)
- ✅ Return machine info

**Main Preview** (`/preview/upload` → `createPreviewForUser`):

- ✅ Quick health check (machine should already be ready)
- ✅ Code injection (fast, <5s)
- ✅ Schedule suspension

### Phase 5: Frontend Integration

#### 5.1 Chat Submit Optimization

- **Location**: `apps/admin/src/features/chat/main.tsx` - `handleSubmit` function (around line 297)
- **Action**: Call machine creation in background during chat submit
- **Context**: This is the main chat component where users submit messages to generate code
- **Implementation**:

  ```typescript
  const handleSubmit = () => {
    setBuildError('');
    setShowErrorCard(false);
    setView('code');
    setLogs('');
    setError(undefined);
    setShowLogs(false);

    // Start machine creation in background (no await needed)
    createMachineInBackground(userAppName);

    handleChatSubmit(undefined, {
      experimental_attachments: files,
    });

    setFiles(undefined);

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    filePreviews.forEach((preview) => {
      URL.revokeObjectURL(preview.url);
    });

    setFilePreviews([]);
  };
  ```

#### 5.2 Frontend Suspension Management

- **Location**: `apps/admin/src/features/chat/main.tsx` - Add suspension timeout state management
- **Action**: Implement timeout management with cancellation
- **Context**: Manage machine suspension timeout in the main chat component state
- **Implementation**:

  ```typescript
  let suspensionTimeout: NodeJS.Timeout;

  // On new chat start
  if (suspensionTimeout) {
    clearTimeout(suspensionTimeout);
  }

  // After preview is done
  suspensionTimeout = setTimeout(
    () => {
      callSuspensionAPI(appName, machineId);
    },
    2 * 60 * 1000,
  ); // 2 minutes
  ```

#### 5.3 Logout Cleanup

- **Location**:
  - **Frontend**: `apps/admin/src/components/side-panel/main.tsx` - Logout button onClick handler (line 60-78)
  - **Backend**: `apps/core/src/preview/preview.controller.ts` - `/preview/cleanup` DELETE endpoint
- **Action**: Backend retrieves user's fly app name and cleans up machine
- **Context**: The logout button is in the sidebar component that appears on all dashboard pages
- **Implementation**:

  ```typescript
  // Frontend - On user logout (simplified)
  await cleanupUserMachine(); // No need to pass app name

  // Backend - Get user's fly app name and cleanup
  async cleanupUserMachine(userId: string) {
    const user = await this.userService.findById(userId);
    if (user?.fly_app_name) {
      await this.previewService.cleanupUserMachine(user.fly_app_name);
    }
  }
  ```

### Phase 6: Database Tracking

#### 6.1 Machine State Tracking - SINGLE MACHINE APPROACH

- **Location**:
  - **Update**: `packages/database/prisma/schema/user.prisma` - Add fly_app_name field and relationship
  - **Create**: `packages/database/prisma/schema/userMachine.prisma` - New model file (already exists)
- **Action**: Create table to track single active machine per user
- **Context**: The UserMachine model already exists, but needs migration to be functional
- **Schema**:

  ```prisma
  model User {
    // ... existing fields
    fly_app_name String? // Store the fly app name for persistent apps
    user_machine UserMachine? // Single machine relationship
  }
  
  model UserMachine {
    id        String   @id @default(cuid())
    userId    Int      @unique // Unique constraint enforces 1 machine per user
    appName   String // Same as user.fly_app_name
    machineId String   @unique // Current machine ID (changes over time)
    status    String // 'creating' | 'running' | 'suspended' | 'destroyed'
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
  
    user User @relation(fields: [userId], references: [id])
  
    @@map("user_machines")
  }
  ```

#### 6.2 Single Machine Management Methods

- **Key Benefits**:

  - ✅ **1 machine per user** - Simple and efficient
  - ✅ **Unique constraints** - Database enforces business rules
  - ✅ **Direct lookups** - No arrays, no loops, no complex queries
  - ✅ **Clean state management** - Single state machine per user

- **Core Methods**:
  ```typescript
  async getCurrentUserMachine(userId: number): Promise<UserMachine | null>
  async setUserMachine(userId: number, appName: string, machineId: string): Promise<void>
  async updateMachineStatus(userId: number, status: string): Promise<void>
  async removeMachineTracking(userId: number): Promise<void>
  ```

## Implementation Order

1. **Database Updates** - Add fly_app_name to User model
2. **FlyPreviewManager Methods** - Add suspend, start, get machine methods ✅ **COMPLETED**
3. **Backend APIs** - Create new endpoints ✅ **PARTIALLY COMPLETED** (cleanup endpoint done)
4. **Service Layer** - Implement business logic ✅ **COMPLETED**
5. **User Registration** - Add app creation to signup
6. **Frontend Integration** - Update chat and logout flows ✅ **LOGOUT COMPLETED**
7. **Testing & Validation**

## Critical Commands to Run

**From project root directory:**

```bash
# 1. Generate Prisma client with UserMachine model
cd packages/database
pnpx prisma migrate dev --name add_user_machine_tracking
pnpx prisma generate

# 2. Install any missing dependencies
cd ../../apps/core
pnpm install

# 3. Restart backend to load new Prisma types
pnpm run dev
```

## Key Benefits

- **Startup Time**: Reduced from 30-40s to ~5-10s (machine reactivation time)
- **Cost Efficiency**: Suspended machine consumes minimal resources
- **User Experience**: Faster preview generation
- **Resource Management**: Better control over machine lifecycle
- **Single Machine Simplicity**:
  - ✅ **1 machine per user** - No complex array handling
  - ✅ **Direct database lookups** - No loops, single machine lookup
  - ✅ **Cleaner state management** - Simple status tracking
  - ✅ **Database constraints** - Unique constraints enforce business rules
  - ✅ **Easier debugging** - Clear 1:1 user-to-machine relationship

## Risk Mitigation

- **Fallback Logic**: If reactivation fails, create new machine
- **Cleanup Strategy**: Automatic machine deletion after extended inactivity
- **Error Handling**: Comprehensive error states and user feedback
- **Resource Limits**: Ensure single machine per user constraint

## Testing Strategy

1. **Unit Tests**: Individual API endpoints and methods
2. **Integration Tests**: Complete flow from chat to preview
3. **Load Tests**: Multiple concurrent users
4. **Error Scenarios**: Network failures, timeout handling
5. **Resource Monitoring**: Memory and CPU usage tracking

## Current Implementation Status

### ✅ **COMPLETED**

- **Health Check Separation**: Two distinct health check types with clear naming:
  - `checkMachineStatus()` - Machine infrastructure health (fast)
  - `checkApplicationHealth()` - Application HTTP response health (slower)
  - `performFullHealthCheck()` - Comprehensive check combining both
- **Backend Single Machine Logic**: All methods updated to handle 1 machine per user
- **Logout Cleanup**: `apps/admin/src/components/side-panel/main.tsx` - Calls `/preview/cleanup` API
- **API Endpoints**: DELETE `/preview/cleanup` endpoint implemented
- **FlyPreviewManager**: Updated with `getUserMachine()`, `deleteUserMachine()`, suspension methods
- **PreviewService**: Updated with single machine tracking and cleanup methods

### 🔧 **IN PROGRESS / NEEDS COMPLETION**

- **Database Migration**: UserMachine model exists but needs `pnpm prisma migrate dev`
- **User Registration**: Add fly app creation to signup flow
- **Chat Integration**: Background machine creation in `apps/admin/src/features/chat/main.tsx`
- **Suspension Management**: Frontend timeout handling in chat component

### 📍 **KEY IMPLEMENTATION LOCATIONS**

- **Main Chat Component**: `apps/admin/src/features/chat/main.tsx` (handleSubmit around line 297)
- **Sidebar Logout**: `apps/admin/src/components/side-panel/main.tsx` (onClick around line 60-78) ✅
- **Auth Service**: `apps/core/src/coreapi/auth/auth.service.ts` (user registration method)
- **Preview Controller**: `apps/core/src/preview/preview.controller.ts` ✅
- **User Schema**: `packages/database/prisma/schema/user.prisma`

## Timeline Estimate

- **Remaining Work**: 2-3 days (Database migration + User registration + Chat integration)
- **Testing**: 1-2 days
- **Total Remaining**: 3-5 days

## Success Metrics

- Preview startup time < 10 seconds
- 95% success rate for machine reactivation
- Reduced fly.io resource consumption
- Improved user satisfaction scores
