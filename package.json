{"name": "iammoderator", "private": true, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "prepare": "husky", "browser": "npx @agentdeskai/browser-tools-server@latest"}, "devDependencies": {"@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "@types/node": "^22.10.5", "husky": "^9.1.7", "lint-staged": "^15.2.11", "prettier": "^3.3.3", "prettier-plugin-prisma": "^5.0.0", "ts-node": "^10.9.1", "turbo": "^2.3.3", "typescript": "5.5.4"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}, "dependencies": {"@engagespot/node": "^1.7.0"}}